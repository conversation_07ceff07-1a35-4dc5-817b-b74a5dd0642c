{
    'name': 'ABS Payroll',
    'author': "TH Company",
    'summary': 'ABS Payroll',
    'category': 'AUM Business System/ Payroll',
    'website': 'https://aum.edu.vn/',
    'license': 'LGPL-3',
    'version': '16.0.28022025',
    'depends': [
        'base',
        'th_contract',
        'th_work_leave',
        'th_employee',
        'hr_payroll',
        'th_core_access_control',
    ],
    'data': [
        'security/th_access_groups.xml',
        'security/ir.model.access.csv',
        'security/th_template_groups.xml',
        'security/payroll_security.xml',
        'data/hr_contract_type_data.xml',
        'data/sequence.xml',
        'report/actions.xml',
        'report/th_payroll_reports.xml',
        'report/th_payslip_template.xml',
        'report/overtime_request_template.xml',
        'report/salary_advance_request_template.xml',
        'report/th_aum_report_view.xml',
        'views/hr_payslip_input_type_view_form.xml',
        'views/hr_contract_type.xml',
        'views/hr_payslip.xml',
        'views/template_payroll.xml',
        'views/th_kpi_type.xml',
        'views/th_kpi.xml',
        'views/hr_contract.xml',
        'views/hr_payroll_structure_type_views.xml',
        'views/hr_payroll_structure_views.xml',
        'data/ir_cron.xml',
        'views/salary_advance_request_views.xml',
        'views/overtime_request_views.xml',
        'views/setting_views.xml',
        'views/hr_work_entry_type.xml',
        'views/hr_salary_rule.xml',
        'views/hr_payslip_run.xml',
        'views/hr_payslip_employee.xml',
        'views/hr_employee_views.xml',
        'views/menu.xml',
        'wizard/th_update_work_tries_wizard.xml',
        'wizard/personal_tax_report_views.xml',
        'wizard/action.xml',
        'wizard/select_report_view.xml',
        'wizard/import_contract_wizard_view.xml',
        'wizard/hr_work_entry_regeneration_wizard.xml',
        'wizard/hr_payroll_payslips_by_employees_views.xml',
        'wizard/th_upload_kpi.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'th_payroll/static/src/**/*',
        ],
        'web.assets_qweb': [
            'th_payroll/static/src/xml/**/*',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
}
