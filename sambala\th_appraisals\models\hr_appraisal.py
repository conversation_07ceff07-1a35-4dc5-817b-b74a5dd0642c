# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from datetime import date
from dateutil.relativedelta import relativedelta


class HrAppraisal(models.Model):
    _inherit = 'hr.appraisal'

    employee_feedback_published = fields.Boolean(string="Employee Feedback Published", tracking=True, default=True)
    manager_feedback_published = fields.Boolean(string="Manager Feedback Published", tracking=True, default=True)
    date_close = fields.Date(
        string='Appraisal Date',
        help='Date of the appraisal, automatically updated when the appraisal is Done or Cancelled.', required=True,
        index=True,
        default=lambda self: date.today() + relativedelta(days=+7))
    hr_appraisal_run_id = fields.Many2one('hr.appraisal.run', string='Appraisal run', ondelete='restrict')
    hr_appraisal_type_id = fields.Many2one('hr.appraisal.type', string='Appraisal type', ondelete='restrict')
    state = fields.Selection(
        [('new', 'CHỜ XÁC NHẬN'),
         ('pending', 'ĐÃ ĐƯỢC XÁC NHẬN'),
         ('manager_approved', 'QLTT ĐÃ XÁC NHẬN'),
         ('done', 'HOÀN THÀNH'),
         ('cancel', "ĐÃ HỦY")],
        string='Status', tracking=True, required=True, copy=False,
        default='new', index=True, group_expand='_group_expand_states')
    th_check_user = fields.Boolean(string="Kiểm tra user là nhân viên", compute='_th_check_user', default=False)

    @api.depends('manager_ids')
    def _th_check_user(self):
        """Check if the current user is an employee."""
        for rec in self:
            if rec.env.user.employee_id.id in rec.manager_ids.ids:
                rec.th_check_user = True
            else:
                rec.th_check_user = False

    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        self = self.sudo()  # fields are not on the employee public
        if self.employee_id:
            manager_ids = []
            direct_manager = self.employee_id.parent_id.id
            indirect_manager = self.employee_id.parent_id.parent_id.id
            if direct_manager:
                manager_ids.append(direct_manager)
            if indirect_manager:
                manager_ids.append(indirect_manager)
            self.manager_ids = [(6, 0, manager_ids)]

    @api.depends('department_id', 'hr_appraisal_type_id')
    def _compute_employee_feedback(self):
        for appraisal in self.filtered(lambda a: a.state == 'new'):
            if appraisal.hr_appraisal_type_id:
                appraisal.employee_feedback = appraisal.hr_appraisal_type_id.appraisal_employee_feedback_template
                continue
            appraisal.employee_feedback = appraisal.department_id.employee_feedback_template if appraisal.department_id.custom_appraisal_templates \
                else appraisal.company_id.appraisal_employee_feedback_template

    @api.depends('department_id', 'hr_appraisal_type_id')
    def _compute_manager_feedback(self):
        for appraisal in self.filtered(lambda a: a.state == 'new'):
            if appraisal.hr_appraisal_type_id:
                appraisal.manager_feedback = appraisal.hr_appraisal_type_id.appraisal_manager_feedback_template
                continue
            appraisal.manager_feedback = appraisal.department_id.manager_feedback_template if appraisal.department_id.custom_appraisal_templates \
                else appraisal.company_id.appraisal_manager_feedback_template

    def th_action_manager_confirm(self):
        self.state = 'manager_approved'