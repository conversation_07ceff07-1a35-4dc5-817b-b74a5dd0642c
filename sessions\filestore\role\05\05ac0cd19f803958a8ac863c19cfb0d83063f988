
/* <inline asset> */
@charset "UTF-8"; 

/* /web_editor/static/lib/cropperjs/cropper.css */
 .cropper-container{direction: ltr; font-size: 0; line-height: 0; position: relative; -ms-touch-action: none; touch-action: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;}.cropper-container img{display: block; height: 100%; image-orientation: 0deg; max-height: none !important; max-width: none !important; min-height: 0 !important; min-width: 0 !important; width: 100%;}.cropper-wrap-box, .cropper-canvas, .cropper-drag-box, .cropper-crop-box, .cropper-modal{bottom: 0; left: 0; position: absolute; right: 0; top: 0;}.cropper-wrap-box, .cropper-canvas{overflow: hidden;}.cropper-drag-box{background-color: #fff; opacity: 0;}.cropper-modal{background-color: #000; opacity: 0.5;}.cropper-view-box{display: block; height: 100%; outline: 1px solid #39f; outline-color: rgba(51, 153, 255, 0.75); overflow: hidden; width: 100%;}.cropper-dashed{border: 0 dashed #eee; display: block; opacity: 0.5; position: absolute;}.cropper-dashed.dashed-h{border-bottom-width: 1px; border-top-width: 1px; height: calc(100% / 3); left: 0; top: calc(100% / 3); width: 100%;}.cropper-dashed.dashed-v{border-left-width: 1px; border-right-width: 1px; height: 100%; left: calc(100% / 3); top: 0; width: calc(100% / 3);}.cropper-center{display: block; height: 0; left: 50%; opacity: 0.75; position: absolute; top: 50%; width: 0;}.cropper-center::before, .cropper-center::after{background-color: #eee; content: ' '; display: block; position: absolute;}.cropper-center::before{height: 1px; left: -3px; top: 0; width: 7px;}.cropper-center::after{height: 7px; left: 0; top: -3px; width: 1px;}.cropper-face, .cropper-line, .cropper-point{display: block; height: 100%; opacity: 0.1; position: absolute; width: 100%;}.cropper-face{background-color: #fff; left: 0; top: 0;}.cropper-line{background-color: #39f;}.cropper-line.line-e{cursor: ew-resize; right: -3px; top: 0; width: 5px;}.cropper-line.line-n{cursor: ns-resize; height: 5px; left: 0; top: -3px;}.cropper-line.line-w{cursor: ew-resize; left: -3px; top: 0; width: 5px;}.cropper-line.line-s{bottom: -3px; cursor: ns-resize; height: 5px; left: 0;}.cropper-point{background-color: #39f; height: 5px; opacity: 0.75; width: 5px;}.cropper-point.point-e{cursor: ew-resize; margin-top: -3px; right: -3px; top: 50%;}.cropper-point.point-n{cursor: ns-resize; left: 50%; margin-left: -3px; top: -3px;}.cropper-point.point-w{cursor: ew-resize; left: -3px; margin-top: -3px; top: 50%;}.cropper-point.point-s{bottom: -3px; cursor: s-resize; left: 50%; margin-left: -3px;}.cropper-point.point-ne{cursor: nesw-resize; right: -3px; top: -3px;}.cropper-point.point-nw{cursor: nwse-resize; left: -3px; top: -3px;}.cropper-point.point-sw{bottom: -3px; cursor: nesw-resize; left: -3px;}.cropper-point.point-se{bottom: -3px; cursor: nwse-resize; height: 20px; opacity: 1; right: -3px; width: 20px;}@media (min-width: 768px){.cropper-point.point-se{height: 15px; width: 15px;}}@media (min-width: 992px){.cropper-point.point-se{height: 10px; width: 10px;}}@media (min-width: 1200px){.cropper-point.point-se{height: 5px; opacity: 0.75; width: 5px;}}.cropper-point.point-se::before{background-color: #39f; bottom: -50%; content: ' '; display: block; height: 200%; opacity: 0; position: absolute; right: -50%; width: 200%;}.cropper-invisible{opacity: 0;}.cropper-bg{background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC');}.cropper-hide{display: block; height: 0; position: absolute; width: 0;}.cropper-hidden{display: none !important;}.cropper-move{cursor: move;}.cropper-crop{cursor: crosshair;}.cropper-disabled .cropper-drag-box, .cropper-disabled .cropper-face, .cropper-disabled .cropper-line, .cropper-disabled .cropper-point{cursor: not-allowed;}

/* /web/static/lib/bootstrap/scss/_functions.scss */
 

/* /web/static/lib/bootstrap/scss/_mixins.scss */
 

/* /web/static/src/scss/mixins_forwardport.scss */
 

/* /web/static/src/scss/bs_mixins_overrides.scss */
 

/* /web/static/src/legacy/scss/utils.scss */
 we-select.o_we_shape_menu we-button[data-shape] .o_we_shape_btn_content, #oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale, .o_we_cc_preview_wrapper, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="custom"] .o_we_color_btn{position: relative; z-index: 0;}we-select.o_we_shape_menu we-button[data-shape] .o_we_shape_btn_content::before, #oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale::before, .o_we_cc_preview_wrapper::before, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="custom"] .o_we_color_btn::before{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1; background-image: url("/web/static/img/transparent.png"); background-size: 10px auto; border-radius: inherit;}we-select.o_we_shape_menu we-button[data-shape] .o_we_shape_btn_content::after, #oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale::after, .o_we_cc_preview_wrapper::after, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="custom"] .o_we_color_btn::after{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1; background: inherit; border-radius: inherit;}

/* /web_enterprise/static/src/scss/primary_variables.scss */
 

/* /web/static/src/scss/primary_variables.scss */
 

/* /web_enterprise/static/src/core/notifications/notifications.variables.scss */
 

/* /web_enterprise/static/src/webclient/home_menu/home_menu.variables.scss */
 

/* /web_enterprise/static/src/webclient/navbar/navbar.variables.scss */
 

/* /web/static/src/core/notifications/notification.variables.scss */
 

/* /web/static/src/search/control_panel/control_panel.variables.scss */
 

/* /web/static/src/search/search_panel/search_panel.variables.scss */
 

/* /web/static/src/views/form/form.variables.scss */
 

/* /web/static/src/views/kanban/kanban.variables.scss */
 

/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */
 

/* /web/static/src/webclient/navbar/navbar.variables.scss */
 

/* /base/static/src/scss/onboarding.variables.scss */
 

/* /web_editor/static/src/scss/web_editor.variables.scss */
 

/* /web_editor/static/src/scss/wysiwyg.variables.scss */
 

/* /web_gantt/static/src/scss/web_gantt.variables.scss */
 

/* /web_enterprise/static/src/scss/secondary_variables.scss */
 

/* /web/static/src/scss/secondary_variables.scss */
 

/* /web_editor/static/src/scss/secondary_variables.scss */
 

/* /web_editor/static/src/scss/bootstrap_overridden.scss */
 

/* /web/static/src/scss/pre_variables.scss */
 

/* /web/static/lib/bootstrap/scss/_variables.scss */
 

/* /web_editor/static/src/js/editor/odoo-editor/src/style.scss */
 .odoo-editor-editable ::selection{background-color: rgba(117, 167, 249, 0.5) !important;}.odoo-editor-editable.o_col_resize{cursor: col-resize;}.odoo-editor-editable.o_col_resize ::selection{background-color: transparent;}.odoo-editor-editable.o_row_resize{cursor: row-resize;}.odoo-editor-editable.o_row_resize ::selection{background-color: transparent;}.o_selected_table{caret-color: transparent;}.o_selected_table ::selection{background-color: transparent !important;}.o_selected_table .o_selected_td{background-color: rgba(117, 167, 249, 0.5) !important; cursor: pointer !important;}.o_table_ui{background-color: transparent; position: absolute; z-index: 10; padding: 0;}.o_table_ui:hover{visibility: visible !important;}.o_table_ui > div{position: absolute;}.o_table_ui .o_table_ui_menu_toggler{cursor: pointer; background-color: var(--o-table-ui-bg, #FFF); color: var(--o-table-ui-color, #4A4F59); border: 1px solid var(--o-table-ui-border, #DEE2E6); border-radius: 5px; padding: 2px 3px;}.o_table_ui .o_table_ui_menu{display: none; cursor: pointer; background-color: var(--o-table-ui-bg, #FFF); width: fit-content; border: 1px solid var(--o-table-ui-border, #DEE2E6); padding: 5px 0; white-space: nowrap;}.o_table_ui .o_table_ui_menu > div:hover{background-color: var(--o-table-ui-hover, #E0E2E6);}.o_table_ui .o_table_ui_menu span{margin-right: 8px; color: var(--o-table-ui-color, #4A4F59);}.o_table_ui .o_table_ui_menu div{padding: 0 8px;}.o_table_ui.o_open{visibility: visible !important;}.o_table_ui.o_open .o_table_ui_menu{display: block;}.o_table_ui.o_open .o_table_ui_menu > div.o_hide{display: none;}.oe-floating{box-shadow: 0px 3px 18px rgba(0, 0, 0, 0.23); border-radius: 4px; position: absolute;}.oe-toolbar{box-sizing: border-box; position: absolute; visibility: hidden; height: fit-content; width: fit-content; padding-left: 5px; padding-right: 5px; background: #222222; color: white; border-radius: 8px;}.oe-toolbar .toolbar-bottom::before{content: ''; position: absolute; width: 0; height: 0; left: var(--arrow-left-pos); top: var(--arrow-top-pos); border: transparent 10px solid; border-bottom: #222222 10px solid; z-index: 0;}.oe-toolbar:not(.toolbar-bottom)::before{content: ''; position: absolute; width: 0; height: 0; left: var(--arrow-left-pos); top: var(--arrow-top-pos); border: transparent 10px solid; border-top: #222222 10px solid; z-index: 0; pointer-events: none;}.oe-toolbar .button-group{display: inline-block; margin-right: 13px;}.oe-toolbar .button-group:last-of-type{margin-right: 0;}.oe-toolbar .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button{position: relative; box-sizing: content-box; display: inline-block; padding: 7px; color: white;}.oe-toolbar .btn:not(.disabled):hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not(.disabled):hover{background: #868686;}.oe-toolbar .oe-toolbar .dropdown-menu .btn, .oe-toolbar.oe-floating .oe-toolbar .dropdown-menu .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .oe-toolbar .dropdown-menu button{background: #222222;}.oe-toolbar .btn.active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active{background: #555555;}.oe-toolbar .dropdown-toggle{background: transparent; border: none; padding: 7px;}.oe-toolbar .dropdown-toggle[aria-expanded="true"]{background: #555555;}.oe-toolbar .dropdown-menu{background: #222222; min-width: max-content; min-width: -webkit-max-content; text-align: center;}.oe-toolbar .dropdown-item{background: transparent; color: white;}.oe-toolbar .dropdown-item pre, .oe-toolbar .dropdown-item h1, .oe-toolbar .dropdown-item h2, .oe-toolbar .dropdown-item h3, .oe-toolbar .dropdown-item h4, .oe-toolbar .dropdown-item h5, .oe-toolbar .dropdown-item h6, .oe-toolbar .dropdown-item blockquote{margin: 0; color: white;}.oe-toolbar .dropdown-item:hover, .oe-toolbar .dropdown-item:focus{color: white; background: #868686;}.oe-toolbar .dropdown-item.active, .oe-toolbar .dropdown-item:active{color: white; background: #555555;}.oe-toolbar li > a.dropdown-item{color: white;}.oe-toolbar label, .oe-toolbar label span{display: inline-block;}.oe-toolbar input[type="color"]{width: 0; height: 0; padding: 0; border: none; box-sizing: border-box; position: absolute; opacity: 0; top: 100%; margin: 2px 0 0;}.oe-toolbar #colorInputButtonGroup label{margin-bottom: 0;}.oe-toolbar .color-indicator{background-color: transparent; padding-bottom: 4px;}.oe-toolbar .color-indicator.fore-color{border-bottom: 2px solid var(--fore-color); padding: 5px;}.oe-toolbar .color-indicator.hilite-color{border-bottom: 2px solid var(--hilite-color); padding: 5px;}.oe-toolbar #style .dropdown-menu{text-align: left;}.oe-tablepicker-dropdown .oe-tablepicker{margin: -3px 2px -6px 2px;}.oe-tablepicker-wrapper.oe-floating{padding: 3px; z-index: 1056; background-color: var(--oeTablepicker__wrapper-bg, #FFF);}.oe-tablepicker-row{line-height: 0;}.oe-tablepicker{width: max-content; width: -webkit-max-content;}.oe-tablepicker .oe-tablepicker-row .oe-tablepicker-cell{display: inline-block; background-color: var(--oeTablepicker__cell-bg, #E0E2E6); width: 19px; height: 19px; padding: 0; margin-right: 3px; margin-bottom: 3px;}.oe-tablepicker .oe-tablepicker-row .oe-tablepicker-cell:last-of-type{margin-right: 0;}.oe-tablepicker .oe-tablepicker-row .oe-tablepicker-cell.active{background-color: var(--oeTablepicker-color-accent, #017e84);}.oe-tablepicker-size{text-align: center; margin-top: 7px;}.oe-tablepicker-dropdown .oe-tablepicker-size{color: white;}@media only screen and (max-width: 767px){.oe-toolbar{position: relative; visibility: visible; width: 100%; border-radius: 0; background-color: white;}.oe-toolbar .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button{color: black;}}.oe-powerbox-wrapper{position: absolute; z-index: 1055; border: black; background: var(--oePowerbox__wrapper-bg, #FFF); color: #4A4F59; max-height: 40vh; box-sizing: border-box; max-width: 100%; box-shadow: 0px 3px 18px rgba(0, 0, 0, 0.23); border-radius: 4px; overflow: hidden; display: -webkit-box; display: -webkit-flex; display: flex;}.oe-powerbox-wrapper ::-webkit-scrollbar{background: transparent;}.oe-powerbox-wrapper ::-webkit-scrollbar{width: 10px; height: 10px;}.oe-powerbox-wrapper ::-webkit-scrollbar-thumb{background: var(--oePowerbox__ScrollbarThumb-background-color, #D3D1CB);}.oe-powerbox-wrapper ::-webkit-scrollbar-track{background: var(--oePowerbox__ScrollbarTrack-background-color, #EDECE9);}.oe-powerbox-mainWrapper{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; overflow: auto; padding: 5px 0; overscroll-behavior: contain;}.oe-powerbox-category, .oe-powerbox-noResult{margin: 10px; color: var(--oePowerbox__category-color, #626774); font-size: 11px;}.oe-powerbox-category{text-transform: uppercase; margin: 5px 12px;}.oe-powerbox-noResult{display: none;}.oe-powerbox-commandWrapper{display: -webkit-box; display: -webkit-flex; display: flex; padding: 6px 12px; cursor: pointer;}.oe-powerbox-commandWrapper.active{background: var(--oePowerbox__commandName-bg, #f6f7fa);}i.oe-powerbox-commandImg{display: -webkit-box; display: -webkit-flex; display: flex; height: 30px; width: 30px; align-items: center; justify-content: center; background: var(--oePowerbox__commandImg-bg, #f6f7fa); color: var(--oePowerbox__commandImg-color, #353840); border: 1px solid rgba(0, 0, 0, 0.1); border-radius: 7px; font-size: 15px;}.oe-powerbox-commandName{font-size: 13px; color: var(--oePowerbox__commandName-color, #4A4F59);}.oe-powerbox-commandDescription{color: var(--oePowerbox__commandDescription-color, rgba(74, 79, 89, 0.76)); font-size: 12px;}.oe-powerbox-commandRightCol{margin: 0 10px;}.oe-hint{position: relative;}.oe-hint:before{content: attr(placeholder); position: absolute; top: 0; left: 0; display: block; color: inherit; opacity: 0.4; pointer-events: none; text-align: inherit; width: 100%;}.oe-collaboration-selections-container{position: absolute; isolation: isolate; height: 0; width: 0; z-index: 1;}.oe-collaboration-caret-top-square{min-height: 5px; min-width: 5px; color: #fff; text-shadow: 0 0 5px #000; position: absolute; bottom: 100%; left: -4px; white-space: nowrap;}.oe-collaboration-caret-top-square:hover{border-radius: 2px; padding: 0.3em 0.6em;}.oe-collaboration-caret-top-square:hover::before{content: attr(data-client-name);}.oe-collaboration-caret-avatar{position: absolute; height: 1.5rem; width: 1.5rem; border-radius: 50%; transition: top 0.5s, left 0.5s;}.oe-collaboration-caret-avatar > img{height: 100%; width: 100%; border-radius: 50%;}.oe-collaboration-caret-avatar[data-overlapping-avatars]::after{content: attr(data-overlapping-avatars); background-color: green; color: white; border-radius: 50%; font-size: 9px; padding: 0 4px; position: absolute; top: 11px; right: -5px; z-index: 1;}code.o_inline_code{background-color: #c5c5c5; padding: 2px; margin: 2px; color: black; font-size: inherit;}

/* /web_editor/static/src/scss/wysiwyg.scss */
 :root{--o-we-toolbar-height: 40px;}.o_we_command_protector{font-weight: 400 !important;}.o_we_command_protector b, .o_we_command_protector strong{font-weight: 700 !important;}.o_we_command_protector *{font-weight: inherit !important;}.o_we_command_protector .btn, .o_we_command_protector .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .o_we_command_protector button{text-align: unset !important;}.wysiwyg_iframe, .note-editor{border: 1px solid #D9D9D9; margin: 0; padding: 0;}.colorpicker{--bg: #FFF; --text-rgb: 43, 43, 51; --border-rgb: var(--text-rgb); --tab-border-top: transparent; --tab-border-bottom: #D9D9D9; --btn-color-active: inset 0 0 0 2px #01bad2, inset 0 0 0 3px var(--bg), inset 0 0 0 4px rgba(var(--border-rgb), .5);}.colorpicker, .colorpicker input{color: rgba(var(--text-rgb), 1);}.colorpicker label{color: rgba(var(--text-rgb), 0.5);}.colorpicker button{outline: none;}.colorpicker .o_we_colorpicker_switch_panel{font-size: 13px; border-bottom: 1px solid var(--tab-border-bottom); box-shadow: inset 0 1px 0 var(--tab-border-top);}.colorpicker .o_we_colorpicker_switch_pane_btn, .colorpicker .o_colorpicker_reset{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}.colorpicker .o_colorpicker_reset{margin-left: auto !important;}.colorpicker .o_colorpicker_sections{background: var(--bg);}.colorpicker .o_colorpicker_sections > *{padding-top: 8px;}.colorpicker .o_colorpicker_sections > *:first-child{padding-top: 0;}.colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_hex_div:focus-within, .colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_rgba_div:focus-within{border-color: #01bad2;}.colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_color_picker_inputs input, .colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_color_picker_inputs input:focus{border: none; outline: none;}.colorpicker .o_colorpicker_sections .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_color_pick_area, .colorpicker .o_colorpicker_sections .o_color_slider, .colorpicker .o_colorpicker_sections .o_opacity_slider:before, .colorpicker .o_colorpicker_sections .o_hex_div, .colorpicker .o_colorpicker_sections .o_rgba_div{box-shadow: inset 0 0 0 1px rgba(var(--border-rgb), 0.5);}.colorpicker .o_colorpicker_sections .o_we_color_btn{position: relative; float: left; width: 12.5%; padding-top: 10%; margin: 0; border: 1px solid var(--bg);}.colorpicker .o_colorpicker_sections .o_we_color_btn.o_colorpicker_reset{background-color: transparent;}.colorpicker .o_colorpicker_sections .o_we_color_btn.o_colorpicker_reset::before{position: absolute; top: 0; left: 0; bottom: 0; right: 0; font-family: FontAwesome !important; content: "\f00d" !important; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; align-items: center; color: #e6586c;}.colorpicker .o_colorpicker_sections .o_we_color_btn.selected{box-shadow: var(--btn-color-active);}.colorpicker .o_colorpicker_sections .o_we_color_btn.o_btn_transparent::before{background-color: transparent;}.colorpicker .o_colorpicker_sections .o_colorpicker_section::after{content: ""; display: table; clear: both;}.colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="transparent_grayscale"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="theme"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="reset"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="custom"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name="custom"] .o_we_color_btn::after{box-shadow: inherit;}.oe-toolbar{display: grid;}.oe-toolbar .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center;}.oe-toolbar .colorpicker-menu{height: auto !important; box-sizing: content-box; min-height: fit-content;}.oe-toolbar .dropdown-item.active:not(.dropdown-item_active_noarrow):before, .oe-toolbar .dropdown-item.selected:not(.dropdown-item_active_noarrow):before{transform: translate(-1.5em, 0); height: 100%; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}.oe-toolbar.oe-floating{gap: 0 0.35em; grid-auto-flow: column; align-items: stretch; height: auto; min-height: 40px; max-width: none; border-radius: 2px; padding: 0 0.5em; background-color: var(--o-we-toolbar-bg, #FFF); box-shadow: 0 0 4px rgba(0, 0, 0, 0.1), 0 4px 18px rgba(0, 0, 0, 0.25); color: var(--o-we-toolbar-color-text, #2b2b33); font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Liberation Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";}.oe-toolbar.oe-floating.toolbar-bottom:before{border-bottom-color: var(--o-we-toolbar-bg, #FFF);}.oe-toolbar.oe-floating:not(.toolbar-bottom):before{border-top-color: var(--o-we-toolbar-bg, #FFF);}.oe-toolbar.oe-floating.noarrow::before{display: none;}.oe-toolbar.oe-floating > .btn-group:not(.d-none) ~ .btn-group:not(.d-none):before, .oe-toolbar.oe-floating .oe-toolbar-separator:before{content: ""; width: 1px; margin-right: calc(0.35em - 1px); background: var(--o-we-toolbar-border, #D9D9D9); transform: scaleY(0.6);}.oe-toolbar.oe-floating > .btn-group:not(.d-none) ~ .btn-group:not(.d-none) .o-dropdown-menu:not([x-placement]), .oe-toolbar.oe-floating .oe-toolbar-separator .o-dropdown-menu:not([x-placement]){margin-left: 0.35em;}.oe-toolbar.oe-floating .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .dropdown-item{padding: 3.5px 7px; color: var(--o-we-toolbar-color-clickable, #595964);}.oe-toolbar.oe-floating .btn:hover:not(.active), .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:hover:not(.active), .oe-toolbar.oe-floating .dropdown-item:hover:not(.active){color: var(--o-we-toolbar-color-clickable-active, #000000); background-color: transparent;}.oe-toolbar.oe-floating .btn.active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active, .oe-toolbar.oe-floating .dropdown-item.active{background: var(--o-we-toolbar-bg-active, rgba(217, 217, 217, 0.2)); box-shadow: inset 0 0 3px RGBA(var(--o-we-toolbar-bg-active, rgba(217, 217, 217, 0.2)), 0.5);}.oe-toolbar.oe-floating .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button{border: none; border-radius: 0; background: transparent; font-weight: 400;}.oe-toolbar.oe-floating .btn.active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active{color: var(--o-we-toolbar-color-accent, #018597);}.oe-toolbar.oe-floating > .btn-group > .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .oe-toolbar.oe-floating > .btn-group > button, .oe-toolbar.oe-floating > .btn-group > .colorpicker-group{margin: 4px auto; padding-top: 0; padding-bottom: 0;}.oe-toolbar.oe-floating .show > .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .show > button, .oe-toolbar.oe-floating .show > .btn:hover, .oe-toolbar.oe-floating .show > .btn:focus{color: var(--o-we-toolbar-color-clickable-active, #000000);}.oe-toolbar.oe-floating .dropdown-toggle::after{content: ""; display: inline-block; width: 0; height: 0; vertical-align: middle; -moz-transform: scale(0.9999); border-bottom: 0; border-left: 0.3em solid transparent; border-right: 0.3em solid transparent; border-top: 0.3em solid var(--o-caret-color, currentColor); margin-left: .3em;}.oe-toolbar.oe-floating .dropdown-menu{margin: 0; border: 0; padding: 0; max-height: none; overflow: visible; border-top: 1px solid var(--o-we-toolbar-border, #D9D9D9); background-color: var(--o-we-toolbar-bg, #FFF); box-shadow: 0 18px 18px rgba(0, 0, 0, 0.23); border-top-left-radius: 0; border-top-right-radius: 0; border-bottom-right-radius: 2px; border-bottom-left-radius: 2px;}.oe-toolbar.oe-floating .dropdown-menu.show{min-width: 0;}.oe-toolbar.oe-floating .dropdown-menu:not(.colorpicker-menu) > li:last-child{margin-bottom: 1em;}.oe-toolbar.oe-floating .dropdown-menu.colorpicker-menu{margin-top: 0; min-width: 222px !important;}.oe-toolbar.oe-floating .dropdown-item{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; -webkit-box-pack: start; justify-content: flex-start; padding: 0 27.2px; min-height: 34px;}.oe-toolbar.oe-floating .dropdown-item > *{color: inherit;}.oe-toolbar.oe-floating .dropdown-item.active > *, .oe-toolbar.oe-floating .dropdown-item.active:hover, .oe-toolbar.oe-floating .dropdown-item.active:focus{color: var(--o-we-toolbar-color-clickable-active, #000000);}.oe-toolbar.oe-floating .dropdown-item.active > *:before, .oe-toolbar.oe-floating .dropdown-item.active:hover:before, .oe-toolbar.oe-floating .dropdown-item.active:focus:before{top: 0; transform: translate(-17px, 0); line-height: 34px;}.oe-toolbar.oe-floating #decoration #removeFormat{display: none;}.oe-toolbar.oe-floating #decoration .active ~ #removeFormat{display: -webkit-box; display: -webkit-flex; display: flex;}.oe-toolbar.oe-floating #colorInputButtonGroup label:last-of-type .btn, .oe-toolbar.oe-floating #colorInputButtonGroup label:last-of-type .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #colorInputButtonGroup label:last-of-type button{margin: 0 1px 0 -1px;}.oe-toolbar.oe-floating #colorInputButtonGroup .note-back-color-preview.dropup .dropdown-menu{left: -52px;}.oe-toolbar.oe-floating .colorpicker-group .dropdown-toggle::after{display: none;}.oe-toolbar.oe-floating .colorpicker-group .colorpicker-menu{bottom: 100%;}.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button{margin-bottom: -1px;}.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active{border-bottom: 1px solid var(--o-we-toolbar-color-accent, #018597); background: none; box-shadow: none; color: var(--o-we-toolbar-color-clickable-active, #000000);}.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset{background: #017e84;}.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset:hover{color: #FFFFFF;}.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset:hover{background: #01666b;}.oe-toolbar.oe-floating .colorpicker{background: var(--o-we-toolbar-bg, #FFF); box-shadow: 0px 3px 9px rgba(0, 0, 0, 0.2);}.oe-toolbar.oe-floating .o_image_alt{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; max-width: 150px;}.oe-tablepicker-wrapper .oe-tablepicker .oe-tablepicker-cell{border-radius: 0;}.oe-tablepicker-wrapper .oe-tablepicker .oe-tablepicker-cell.active{background: var(--o-we-toolbar-color-accent, #018597);}body:not(.editor_has_snippets) .oe-toolbar{z-index: 1056;}@media only screen and (max-width: 767px){.oe-toolbar{background-color: white;}.oe-toolbar .btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button{color: black;}.oe-toolbar::before{display: none;}.oe-toolbar::after{display: none;}}.oe_edited_link{position: relative; display: inline-block;}.oe_edited_link::before{content: ''; border: dashed 3px #01bad2; position: absolute; inset: -5px; pointer-events: none;}.oe_edited_link:empty::after{content: "\00a0\00a0";}@keyframes fadeInDownSmall{0%{opacity: 0; transform: translate(0, -5px);}100%{opacity: 1; transform: translate(0, 0);}}@keyframes inputHighlighter{from{background: #017e84;}to{width: 0; background: transparent;}}.o_we_horizontal_collapse{width: 0 !important; padding: 0 !important; border: none !important;}.o_we_transition_ease{transition: all ease 0.35s;}body .modal .o_link_dialog input.link-style:checked + span::after{content: "\f00c"; display: inline-block; font-family: FontAwesome; margin-left: 2px;}body .modal .o_link_dialog .o_link_dialog_preview{border-left: var(--o-link-dialog-preview-border, 1px solid #DEE2E6);}.o_we_progressbar:last-child hr{display: none;}.fa.o_we_selected_image::before, img.o_we_selected_image{outline: 3px solid rgba(150, 150, 220, 0.3);}.o_we_media_author{font-size: 11px; position: absolute; top: auto; left: 0; bottom: 0; right: 0; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; text-align: center; background-color: rgba(255, 255, 255, 0.7);}@media (max-width: 991.98px){#web_editor-top-edit{position: initial !important; height: initial !important; top: initial !important; left: initial !important;}.oe-toolbar.oe-floating{box-shadow: 0 10px 10px -5px rgba(0, 0, 0, 0.1); display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; margin-bottom: 1rem; overflow-y: visible;}.oe-toolbar.oe-floating .dropdown-menu{max-height: 200px; overflow: auto;}.oe-toolbar.oe-floating .dropdown-menu.colorpicker-menu{bottom: auto;}}.note-editable .modal:not(.o_technical_modal){top: 40px; right: 0; bottom: 0; right: 288px; width: auto; height: auto;}.note-editable .modal:not(.o_technical_modal) .modal-dialog{padding: 0.5rem 0;}.o_wysiwyg_wrapper{position: relative; margin-bottom: 11px;}.o_wysiwyg_resizer{background: #f5f5f5; height: 10px; width: 100%; border-left: 1px solid #D9D9D9; border-bottom: 1px solid #D9D9D9; border-right: 1px solid #D9D9D9; cursor: row-resize; padding-top: 1px;}.o_wysiwyg_resizer_hook{width: 20px; margin: 1px auto; border-top: 1px solid #a9a9a9;}.note-editable{border: 1px solid #D9D9D9; overflow: auto; height: 100%; padding: 4px; min-height: 10px; border-radius: 3px;}.oe-bordered-editor > .note-editable{border-width: 1px; padding: 4px; min-height: 180px;}.o_we_no_pointer_events{pointer-events: none;}.o_we_crop_widget{background-color: rgba(128, 128, 128, 0.5); position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: 1024;}.o_we_crop_widget .o_we_cropper_wrapper{position: absolute;}.o_we_crop_widget .o_we_crop_buttons{margin-top: 0.5rem; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap;}.o_we_crop_widget .o_we_crop_buttons input[type=radio]{display: none;}.o_we_crop_widget .o_we_crop_buttons .btn-group{border-radius: 0.25rem; margin: 0.1rem;}.o_we_crop_widget .o_we_crop_buttons button, .o_we_crop_widget .o_we_crop_buttons label{cursor: pointer !important; padding: 0.2rem 0.3rem;}.o_we_crop_widget .o_we_crop_buttons label{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}.o_we_crop_widget .o_we_crop_buttons label.active{background-color: #000000;}.o_we_crop_widget .o_we_crop_buttons button:not(.btn), .o_we_crop_widget .o_we_crop_buttons label{margin: 0; border: none; border-right: 1px solid #2b2b33; background-color: #2b2b33; color: #D9D9D9;}.o_we_crop_widget .o_we_crop_buttons button:not(.btn):first-child, .o_we_crop_widget .o_we_crop_buttons label:first-child{border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem;}.o_we_crop_widget .o_we_crop_buttons button:not(.btn):last-child, .o_we_crop_widget .o_we_crop_buttons label:last-child{border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; border-right: none;}[data-oe-xpath], [data-oe-xpath] [contenteditable=true]{outline: none;}.o_transform_removal{transform: none !important;}.o_edit_menu_popover{max-width: 331.2px; width: 331.2px; user-select: none; font-size: 12px; font-weight: 400 !important;}.o_edit_menu_popover .fw-bold{font-weight: 500 !important;}.o_edit_menu_popover .o_we_preview_favicon > img{max-height: 16px; max-width: 16px;}.o_edit_menu_popover .o_we_url_link{width: 100px;}.o_edit_menu_popover .o_we_full_url{word-break: break-all; overflow: hidden; text-overflow: ellipsis; -webkit-box-orient: vertical; -webkit-line-clamp: 2;}.o_edit_menu_popover .o_we_full_url.o_we_webkit_box{display: -webkit-box;}.o_edit_menu_popover .o_we_full_url:hover{-webkit-line-clamp: unset;}textarea.o_codeview{min-height: 400px;}

/* /web_editor/static/src/scss/wysiwyg_iframe.scss */
 iframe.wysiwyg_iframe.o_fullscreen{left: 0 !important; right: 0 !important; top: 0 !important; bottom: 0 !important; width: 100% !important; min-height: 100% !important; z-index: 1001 !important; border: 0;}.o_wysiwyg_no_transform{transform: none !important;}body.o_in_iframe{background-color: white;}body.o_in_iframe .o_editable{position: relative;}body.o_in_iframe .note-editable{border: none; padding: 0; border-radius: 0;}body.o_in_iframe #oe_snippets{top: 0;}body.o_in_iframe .iframe-editor-wrapper{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; overflow: auto;}body.o_in_iframe.oe_dropzone_active .note-editable{overflow: hidden;}body.o_in_iframe .iframe-utils-zone{display: -webkit-box; display: -webkit-flex; display: flex;}body.o_in_iframe .note-statusbar{display: none;}body.o_in_iframe #oe_snippets .email_designer_top_actions{display: -webkit-box; display: -webkit-flex; display: flex; margin: auto 9px auto auto;}body.o_in_iframe #oe_snippets .email_designer_top_actions .btn, body.o_in_iframe #oe_snippets .email_designer_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel body.o_in_iframe #oe_snippets .email_designer_top_actions button{align-items: center; width: 24px; height: 24px; background-color: #337ab7; border: 1px solid #2e6da4; border-radius: 4px; padding: 0; margin-left: 5px;}body.o_in_iframe #oe_snippets .email_designer_top_actions .o_fullscreen_btn img{margin: auto;}body.o_in_iframe textarea.o_codeview{position: absolute; font-family: 'Courier New', Courier, monospace; outline: none; resize: none; top: 0; bottom: 0; left: 0; right: 288px; width: calc(100% - 288px); height: 100%; border: none;}body.o_in_iframe .o_height_400, body.o_in_iframe .o_height_400 div.container, body.o_in_iframe .o_height_400 div.row{min-height: 400px;}body.o_in_iframe .o_height_800, body.o_in_iframe .o_height_800 div.container, body.o_in_iframe .o_height_800 div.row{min-height: 800px;}body.o_in_iframe .btn, body.o_in_iframe .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel body.o_in_iframe button{user-select: auto;}

/* /web_editor/static/src/scss/wysiwyg_snippets.scss */
 @media (max-width: 991.98px){body.editor_enable.editor_has_snippets #web_editor-top-edit{position: initial !important; height: initial !important; top: initial !important; left: initial !important;}body.editor_enable.editor_has_snippets #web_editor-top-edit .note-popover .popover{right: 0 !important;}}.oe_snippet{position: relative; z-index: 1041; width: 77px; background-color: #3e3e46;}.oe_snippet.ui-draggable-dragging{transform: rotate(-3deg) scale(1.2); box-shadow: 0 5px 25px -10px black; transition: transform 0.3s, box-shadow 0.3s;}.oe_snippet > .oe_snippet_body{display: none !important;}.oe_snippet .oe_snippet_thumbnail{width: 100%;}.oe_snippet .oe_snippet_thumbnail .oe_snippet_thumbnail_img{width: 100%; padding-top: 75%; background-repeat: no-repeat; background-size: contain; background-position: top center; overflow: hidden;}.oe_snippet .oe_snippet_thumbnail_title{display: none;}.oe_snippet:not(:hover):not(.o_disabled):not(.o_snippet_install){background-color: rgba(62, 62, 70, 0.9);}.oe_snippet:not(:hover):not(.o_disabled):not(.o_snippet_install) .oe_snippet_thumbnail{filter: saturate(0.7); opacity: .9;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button, #oe_snippets .colorpicker .o_colorpicker_reset, #oe_snippets > .o_we_customize_panel #oe-table-options .btn, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, #oe_snippets > .o_we_customize_panel we-button, #oe_snippets > .o_we_customize_panel we-toggler, #oe_snippets > .o_we_customize_panel .o_we_fold_icon, #oe_snippets > .o_we_customize_panel we-button.o_we_link, #oe_snippets > .o_we_customize_panel #removeFormat, #oe_snippets > .o_we_customize_panel #oe-table-delete-table, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets #snippets_menu > button{outline: none; text-decoration: none; line-height: 20px; cursor: pointer;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button[disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > [disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > [disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button[disabled], #oe_snippets .colorpicker [disabled].o_colorpicker_reset, #oe_snippets > .o_we_customize_panel #oe-table-options [disabled].btn, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button[disabled], .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button[disabled], #oe_snippets > .o_we_customize_panel .oe-toolbar [disabled].btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button[disabled], #oe_snippets > .o_we_customize_panel we-button[disabled], #oe_snippets > .o_we_customize_panel we-toggler[disabled], #oe_snippets > .o_we_customize_panel [disabled].o_we_fold_icon, #oe_snippets > .o_we_customize_panel we-button[disabled].o_we_link, #oe_snippets > .o_we_customize_panel [disabled]#removeFormat, #oe_snippets > .o_we_customize_panel [disabled]#oe-table-delete-table, #oe_snippets > .o_we_customize_panel [disabled].o_we_highlight_animated_text, #oe_snippets > #o_scroll #snippet_custom .oe_snippet [disabled].btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button[disabled], .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button[disabled], #oe_snippets .colorpicker [disabled].o_we_colorpicker_switch_pane_btn, #oe_snippets #snippets_menu > button[disabled]{opacity: .5;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets #snippets_menu > button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]):hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]):hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]):hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]):hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]):hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]):hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]):hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover, #oe_snippets #snippets_menu > button:not([disabled]):hover{color: #FFFFFF;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_success, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_success, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_text_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_success, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_text_success, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_success, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_success{color: #40ad67;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_success:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_success:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_hover_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_success:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_hover_success:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_success:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_success:hover{color: #40ad67;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_success, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_success, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_success, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_success, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_success, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_success{color: white; background-color: #40ad67;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_success:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_success:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_success:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_success:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_success:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_success:hover{background-color: #369156;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_info, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_info, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_text_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_info, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_text_info, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_info, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_info{color: #6999a8;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_info:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_info:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_hover_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_info:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_hover_info:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_info:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_info:hover{color: #6999a8;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_info, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_info, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_info, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_info, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_info, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_info{color: white; background-color: #6999a8;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_info:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_info:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_info:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_info:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_info:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_info:hover{background-color: #568695;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_warning, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_warning, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_text_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_warning, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_text_warning, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_warning, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_warning{color: #f0ad4e;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_warning:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_warning:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_hover_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_warning:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_hover_warning:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_warning:hover{color: #f0ad4e;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_warning, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_warning, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_warning, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_warning, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_warning, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_warning{color: white; background-color: #f0ad4e;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_warning:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_warning:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_warning:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_warning:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_warning:hover{background-color: #ed9d2b;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_danger, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_danger, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_text_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_danger, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_text_danger, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_danger, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_danger{color: #e6586c;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_danger:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_danger:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_hover_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_danger:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_hover_danger:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_danger:hover{color: #e6586c;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_danger, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_danger, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_danger, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_danger, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_danger, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_danger{color: white; background-color: #e6586c;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_danger:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_danger:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_danger:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_danger:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_danger:hover{background-color: #e1374f;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_brand_primary, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_brand_primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_text_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_text_brand_primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_text_brand_primary, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_brand_primary, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_brand_primary{color: #017e84;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_brand_primary:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_hover_brand_primary:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_brand_primary:hover{color: #017e84;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_brand_primary, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_brand_primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_brand_primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_brand_primary, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_brand_primary{color: white; background-color: #017e84;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_brand_primary:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).o_we_bg_brand_primary:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_brand_primary:hover{background-color: #015a5e;}#oe_snippets > .o_we_customize_panel .o_we_fold_icon, #oe_snippets > .o_we_customize_panel we-button.o_we_link, #oe_snippets > .o_we_customize_panel #removeFormat, #oe_snippets > .o_we_customize_panel #oe-table-delete-table, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets #snippets_menu > button{color: #9d9d9d;}#oe_snippets > .o_we_customize_panel .o_we_fold_icon svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link svg .o_graphic, #oe_snippets > .o_we_customize_panel #removeFormat svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-delete-table svg .o_graphic, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button svg .o_graphic, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button svg .o_graphic, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn svg .o_graphic, #oe_snippets #snippets_menu > button svg .o_graphic{fill: #9d9d9d;}#oe_snippets > .o_we_customize_panel .o_we_fold_icon svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link svg .o_subdle, #oe_snippets > .o_we_customize_panel #removeFormat svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-delete-table svg .o_subdle, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button svg .o_subdle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button svg .o_subdle, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn svg .o_subdle, #oe_snippets #snippets_menu > button svg .o_subdle{fill: rgba(157, 157, 157, 0.5);}#oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).active svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).active svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).active svg .o_graphic, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).active svg .o_graphic, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active svg .o_graphic, #oe_snippets #snippets_menu > button:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]):hover svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]):hover svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover svg .o_graphic, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]):hover svg .o_graphic, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover svg .o_graphic, #oe_snippets #snippets_menu > button:not([disabled]):hover svg .o_graphic{fill: #FFFFFF;}#oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]).active svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).active svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]).active svg .o_subdle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]).active svg .o_subdle, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active svg .o_subdle, #oe_snippets #snippets_menu > button:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel .o_we_highlight_animated_text:not([disabled]):hover svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]):hover svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover svg .o_subdle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button:not([disabled]):hover svg .o_subdle, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover svg .o_subdle, #oe_snippets #snippets_menu > button:not([disabled]):hover svg .o_subdle{fill: rgba(157, 157, 157, 0.75);}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button, #oe_snippets .colorpicker .o_colorpicker_reset, #oe_snippets > .o_we_customize_panel #oe-table-options .btn, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, #oe_snippets > .o_we_customize_panel we-button, #oe_snippets > .o_we_customize_panel we-toggler{display: block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; padding: 0 6px; border: 1px solid #000000; border-radius: 2px; background-color: #595964; color: #D9D9D9;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > * svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > * svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button svg .o_graphic, #oe_snippets .colorpicker .o_colorpicker_reset svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .btn svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button svg .o_graphic, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler svg .o_graphic{fill: #D9D9D9;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > * svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > * svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button svg .o_subdle, #oe_snippets .colorpicker .o_colorpicker_reset svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .btn svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button svg .o_subdle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler svg .o_subdle{fill: rgba(217, 217, 217, 0.5);}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]):hover svg .o_graphic, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover svg .o_graphic, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > .active:not(.o_we_no_toggle) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > .active:not(.o_we_no_toggle) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.active:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .active.btn:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active:not(.o_we_no_toggle) svg .o_graphic, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button.active:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar .active.btn:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle) svg .o_graphic{fill: #FFFFFF;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]):hover svg .o_subdle, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover svg .o_subdle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > .active:not(.o_we_no_toggle) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > .active:not(.o_we_no_toggle) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.active:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .active.btn:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active:not(.o_we_no_toggle) svg .o_subdle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button.active:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar .active.btn:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle) svg .o_subdle{fill: rgba(157, 157, 157, 0.75);}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > .active:not(.o_we_no_toggle), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > .active:not(.o_we_no_toggle), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.active:not(.o_we_no_toggle), #oe_snippets .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel #oe-table-options .active.btn:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active:not(.o_we_no_toggle), .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > .o_we_customize_panel #oe-table-options button.active:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel .oe-toolbar .active.btn:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle), #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle){background-color: #2b2b33;}#oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets #snippets_menu > button{display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; justify-content: center; min-width: 0; border: none; background-color: transparent; color: inherit; font-weight: normal;}#oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn > span, #oe_snippets #snippets_menu > button > span{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; padding: 0.6em 0.4em 0.5em;}#oe_snippets .colorpicker .active.o_we_colorpicker_switch_pane_btn > span, #oe_snippets #snippets_menu > button.active > span{color: #FFFFFF; box-shadow: inset 0 -2px 0 #01bad2;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div{border: 1px solid #000000; border-radius: 2px; background-color: #2b2b33;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div:focus-within, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div:focus-within{border-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div input, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div input{box-sizing: content-box; padding: 0 6px; border: none; border-radius: 0; background-color: transparent; color: inherit;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div input:focus, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div input:focus{outline: none;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div > we-button, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div > we-button{border: none;}#oe_snippets{position: absolute; top: var(--o-we-toolbar-height); left: auto; bottom: 0; right: 0; position: fixed; z-index: 1041; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: column nowrap; flex-flow: column nowrap; width: 288px; border-left: 1px solid #2b2b33; background-color: #2b2b33; color: #D9D9D9; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Liberation Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; font-size: 12px; font-weight: 400; transition: transform 400ms ease 0s; transform: translateX(100%);}#oe_snippets *::selection{background: #03e1fe; color: #000000;}#oe_snippets .o_we_website_top_actions{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-pack: start; justify-content: flex-start; width: 288px; height: 46px; min-height: 46px; background-color: #141217;}#oe_snippets .o_we_website_top_actions .btn-group, #oe_snippets .o_we_website_top_actions .btn, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button{height: 100%;}#oe_snippets .o_we_website_top_actions .btn, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button{border: none; border-radius: 0; padding: 0.375rem 0.75rem; font-size: 13px; font-weight: 400; line-height: 1;}#oe_snippets .o_we_website_top_actions .btn:not(.fa), #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:not(.fa), .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button:not(.fa){font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Liberation Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";}#oe_snippets .o_we_website_top_actions .btn.btn-primary, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary{color: #FFFFFF; background-color: #017e84; border-color: #017e84;}#oe_snippets .o_we_website_top_actions .btn.btn-primary:hover, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary:hover{color: #FFFFFF; background-color: #016b70; border-color: #01656a;}.btn-check:focus + #oe_snippets .o_we_website_top_actions .btn.btn-primary, .btn-check:focus + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:focus + #oe_snippets .o_we_website_top_actions button.btn-primary, #oe_snippets .o_we_website_top_actions .btn.btn-primary:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary:focus{color: #FFFFFF; background-color: #016b70; border-color: #01656a; box-shadow: 0 0 0 0.25rem rgba(39, 145, 150, 0.5);}.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-primary, .btn-check:checked + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:checked + #oe_snippets .o_we_website_top_actions button.btn-primary, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-primary, .btn-check:active + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:active + #oe_snippets .o_we_website_top_actions button.btn-primary, #oe_snippets .o_we_website_top_actions .btn.btn-primary:active, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary:active, #oe_snippets .o_we_website_top_actions .btn.btn-primary.active, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary.active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary.active, .show > #oe_snippets .o_we_website_top_actions .btn.btn-primary.dropdown-toggle, .show > #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary.dropdown-toggle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .show > #oe_snippets .o_we_website_top_actions button.btn-primary.dropdown-toggle{color: #FFFFFF; background-color: #01656a; border-color: #015f63;}.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-primary:focus, .btn-check:checked + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:checked + #oe_snippets .o_we_website_top_actions button.btn-primary:focus, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-primary:focus, .btn-check:active + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:active + #oe_snippets .o_we_website_top_actions button.btn-primary:focus, #oe_snippets .o_we_website_top_actions .btn.btn-primary:active:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:active:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary:active:focus, #oe_snippets .o_we_website_top_actions .btn.btn-primary.active:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary.active:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary.active:focus, .show > #oe_snippets .o_we_website_top_actions .btn.btn-primary.dropdown-toggle:focus, .show > #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary.dropdown-toggle:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .show > #oe_snippets .o_we_website_top_actions button.btn-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(39, 145, 150, 0.5);}#oe_snippets .o_we_website_top_actions .btn.btn-primary:disabled, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary:disabled, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary:disabled, #oe_snippets .o_we_website_top_actions .btn.btn-primary.disabled, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-primary.disabled, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-primary.disabled{color: #FFFFFF; background-color: #017e84; border-color: #017e84;}#oe_snippets .o_we_website_top_actions .btn.btn-secondary, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary{color: #FFFFFF; background-color: #141217; border-color: #141217;}#oe_snippets .o_we_website_top_actions .btn.btn-secondary:hover, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:hover, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary:hover{color: #FFFFFF; background-color: #110f14; border-color: #100e12;}.btn-check:focus + #oe_snippets .o_we_website_top_actions .btn.btn-secondary, .btn-check:focus + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:focus + #oe_snippets .o_we_website_top_actions button.btn-secondary, #oe_snippets .o_we_website_top_actions .btn.btn-secondary:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary:focus{color: #FFFFFF; background-color: #110f14; border-color: #100e12; box-shadow: 0 0 0 0.25rem rgba(55, 54, 58, 0.5);}.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-secondary, .btn-check:checked + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:checked + #oe_snippets .o_we_website_top_actions button.btn-secondary, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-secondary, .btn-check:active + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:active + #oe_snippets .o_we_website_top_actions button.btn-secondary, #oe_snippets .o_we_website_top_actions .btn.btn-secondary:active, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary:active, #oe_snippets .o_we_website_top_actions .btn.btn-secondary.active, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary.active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary.active, .show > #oe_snippets .o_we_website_top_actions .btn.btn-secondary.dropdown-toggle, .show > #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary.dropdown-toggle, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .show > #oe_snippets .o_we_website_top_actions button.btn-secondary.dropdown-toggle{color: #FFFFFF; background-color: #100e12; border-color: #0f0e11;}.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-secondary:focus, .btn-check:checked + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:checked + #oe_snippets .o_we_website_top_actions button.btn-secondary:focus, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-secondary:focus, .btn-check:active + #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-check:active + #oe_snippets .o_we_website_top_actions button.btn-secondary:focus, #oe_snippets .o_we_website_top_actions .btn.btn-secondary:active:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:active:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary:active:focus, #oe_snippets .o_we_website_top_actions .btn.btn-secondary.active:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary.active:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary.active:focus, .show > #oe_snippets .o_we_website_top_actions .btn.btn-secondary.dropdown-toggle:focus, .show > #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary.dropdown-toggle:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .show > #oe_snippets .o_we_website_top_actions button.btn-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(55, 54, 58, 0.5);}#oe_snippets .o_we_website_top_actions .btn.btn-secondary:disabled, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary:disabled, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary:disabled, #oe_snippets .o_we_website_top_actions .btn.btn-secondary.disabled, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.btn-secondary.disabled, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button.btn-secondary.disabled{color: #FFFFFF; background-color: #141217; border-color: #141217;}#oe_snippets .o_we_website_top_actions .btn:focus, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:focus, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button:focus, #oe_snippets .o_we_website_top_actions .btn:active, #oe_snippets .o_we_website_top_actions .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:active, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets .o_we_website_top_actions button:active, #oe_snippets .o_we_website_top_actions .btn:focus:active{outline: none; box-shadow: none !important;}#oe_snippets .o_we_website_top_actions .dropdown-menu{left: auto; right: 0;}#oe_snippets #snippets_menu{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: -webkit-box; display: -webkit-flex; display: flex; background-color: #141217; box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2); color: #D9D9D9;}#oe_snippets .o_snippet_search_filter{position: relative; box-shadow: inset 0 -1px 0 #000000, 0 10px 10px rgba(0, 0, 0, 0.2); z-index: 2;}#oe_snippets .o_snippet_search_filter, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input{width: 100%;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input{background-color: #2b2b33; padding: 10px 2em 10px 10px; border: 0; border-bottom: 1px solid #000000; color: #FFFFFF;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input::placeholder{font-style: italic; color: #9d9d9d;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input:focus{background-color: #3e3e46; outline: none;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset{position: absolute; top: 10px; left: auto; bottom: 10px; right: 10px; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; align-items: center; padding: 0 6px; color: #9d9d9d; cursor: pointer;}#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset:hover, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset:focus, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset.focus{color: #FFFFFF;}#oe_snippets > #o_scroll, #oe_snippets > .o_we_customize_panel{min-height: 0; overflow: auto;}#oe_snippets > #o_scroll{background-color: #191922; padding: 0 10px; height: 100%; z-index: 1;}#oe_snippets > #o_scroll .o_panel, #oe_snippets > #o_scroll .o_panel_header{padding: 10px 0;}#oe_snippets > #o_scroll .o_panel_body{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; margin-left: -2px;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%; background-clip: padding-box; border-left: 2px solid transparent; margin-bottom: 2px; user-select: none; cursor: url(/web/static/img/openhand.cur), grab;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet .oe_snippet_thumbnail_title{display: block; padding: 5px; text-align: center;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled .o_snippet_undroppable{position: absolute; top: 8px; left: auto; bottom: auto; right: 6px;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install .btn.o_install_btn, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_install_btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install button.o_install_btn{position: absolute; top: 10px; left: auto; bottom: auto; right: auto;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install:not(:hover) .btn.o_install_btn, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install:not(:hover) .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_install_btn, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install:not(:hover) button.o_install_btn{display: none;}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install{background-color: rgba(62, 62, 70, 0.2);}#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled .oe_snippet_thumbnail_img, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install .oe_snippet_thumbnail_img{opacity: .4; filter: saturate(0) blur(1px);}#oe_snippets > #o_scroll #snippet_custom .oe_snippet{width: 100%;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button{display: -webkit-box; display: -webkit-flex; display: flex;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button{align-items: center;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail{min-width: 0;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail_title{display: block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail_img{flex-shrink: 0; width: 41px; height: 30px; padding: 0;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet button{padding-top: 0; padding-bottom: 0; padding-left: 0;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet:not(:hover) .btn, #oe_snippets > #o_scroll #snippet_custom .oe_snippet:not(:hover) .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #oe_snippets > #o_scroll #snippet_custom .oe_snippet:not(:hover) button{display: none;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget{cursor: pointer;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div{display: -webkit-box; display: -webkit-flex; display: flex;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget input{cursor: text;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button{cursor: pointer; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; line-height: 17px; text-align: center;}#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:hover{background-color: gray;}#oe_snippets > .o_we_customize_panel{position: relative; flex: 1;}#oe_snippets > .o_we_customize_panel .oe-toolbar{position: relative; background: transparent; margin-top: 8px; padding: 0 10px 0 15px; grid-template-areas: "typo typo style colors" "size align list link" "options options options options" "options2 options2 options2 options2"; grid-template-columns: 1fr 1fr 1fr 1fr; grid-template-rows: minmax(22px, auto) minmax(22px, auto) auto auto; row-gap: 8px; column-gap: 3px; width: 100%;}#oe_snippets > .o_we_customize_panel .oe-toolbar::before{display: none;}#oe_snippets > .o_we_customize_panel .oe-toolbar .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button{display: -webkit-box; display: -webkit-flex; display: flex; padding: 2.64px 4px;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options{grid-area: options; padding-left: 0; padding-right: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-title.o_short_title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar we-customizeblock-option .o_short_title.oe-table-label, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-title.o_short_title, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .o_short_title.oe-table-label{width: unset !important; padding-right: 0 !important;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-title.o_long_title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar we-customizeblock-option .o_long_title.oe-table-label, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-title.o_long_title, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .o_long_title.oe-table-label{width: fit-content !important; padding-right: 10px !important;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .highlighted-text, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .highlighted-text{color: white; font-weight: bold; padding: 1px;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown{position: unset; width: -webkit-fill-available;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-toggle, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-toggle{padding: 0; width: inherit;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-toggle::after, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-toggle::after{display: none;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show{position: absolute !important; padding: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show we-button:not(.fa), #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show we-button:not(.fa){text-align: left; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Liberation Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; font-size: 12px; font-weight: 400;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show we-button:not(.fa) div, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show we-button:not(.fa) div{width: 100%;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option input::placeholder, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options input::placeholder{font-style: italic;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-input:not(.o_we_small_input), #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-input:not(.o_we_small_input), #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-input:not(.o_we_small_input) div, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-input:not(.o_we_small_input) div, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-input:not(.o_we_small_input) input, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-input:not(.o_we_small_input) input{width: 100% !important;}#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option + we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options + we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option + #oe-table-options, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options + #oe-table-options{grid-area: options2;}#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup{position: static; grid-area: colors;}#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup .dropdown-toggle:after{display: none;}#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup .colorpicker-group{display: -webkit-box; display: -webkit-flex; display: flex; align-items: stretch; position: static;}#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup #oe-text-color{border-right: 0; border-top-right-radius: 0; border-bottom-right-radius: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup #oe-fore-color{border-top-left-radius: 0; border-bottom-left-radius: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar .btn + .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button + .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn + button, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button + button{border-top-left-radius: 0; border-bottom-left-radius: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar .btn-group > .btn:not(:last-of-type), #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel .btn-group > button:not(:last-of-type){border-top-right-radius: 0; border-bottom-right-radius: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar #list{grid-area: list;}#oe_snippets > .o_we_customize_panel .oe-toolbar #link{grid-area: link;}#oe_snippets > .o_we_customize_panel .oe-toolbar #link #unlink{display: none;}#oe_snippets > .o_we_customize_panel .oe-toolbar #font-size{grid-area: size;}#oe_snippets > .o_we_customize_panel .oe-toolbar #decoration{grid-area: style;}#oe_snippets > .o_we_customize_panel .oe-toolbar #style{grid-area: typo;}#oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle{justify-content: space-between;}#oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle span{color: white;}#oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle span pre, #oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle span blockquote{padding: 0; border: 0; color: inherit;}#oe_snippets > .o_we_customize_panel .oe-toolbar #justify{grid-area: align;}#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu{padding: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating #justify .dropdown-menu .colorpicker-group .o_we_colorpicker_switch_panel button, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #justify .dropdown-menu button{padding: 6.6px 11px; border-width: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu .btn:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating #justify .dropdown-menu .colorpicker-group .o_we_colorpicker_switch_panel button:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #justify .dropdown-menu button:hover{z-index: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu .btn + .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating #justify .dropdown-menu .colorpicker-group .o_we_colorpicker_switch_panel button + .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #justify .dropdown-menu button + .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating #justify .dropdown-menu .colorpicker-group .o_we_colorpicker_switch_panel .btn + button, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #justify .dropdown-menu .btn + button, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating #justify .dropdown-menu .colorpicker-group .o_we_colorpicker_switch_panel button + button, #oe_snippets > .o_we_customize_panel .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel #justify .dropdown-menu button + button{border-left-width: 1px;}#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-menu.colorpicker-menu{min-width: 0; max-height: none; left: 15px; right: 10px; border: 1px solid #000000; border-radius: 2px; padding: 0;}#oe_snippets > .o_we_customize_panel .oe-toolbar :not(.dropup) > .dropdown-menu.colorpicker-menu{top: 2em;}#oe_snippets > .o_we_customize_panel .link-custom-color-border we-input, #oe_snippets > .o_we_customize_panel .link-custom-color-border we-select{max-width: max-content;}#oe_snippets > .o_we_customize_panel .link-custom-color-border we-toggler{width: 85px !important;}#oe_snippets > .o_we_customize_panel we-button.o_we_link{margin-top: 0; border: 0; padding: 0; background: 0;}#oe_snippets > .o_we_customize_panel we-toggler{padding-right: 2em; text-align: left;}#oe_snippets > .o_we_customize_panel we-toggler::after{content: ""; color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-toggler > img, #oe_snippets > .o_we_customize_panel we-toggler > svg{max-width: 100%;}#oe_snippets > .o_we_customize_panel we-toggler + *{display: none !important; border: 1px solid #000000; border-radius: 2px; background-color: #141217; box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.5);}#oe_snippets > .o_we_customize_panel we-toggler.active{padding-right: 2em;}#oe_snippets > .o_we_customize_panel we-toggler.active::after{content: ""; color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-toggler.active + *{display: block !important;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button, #oe_snippets > .o_we_customize_panel we-toggler, #oe_snippets > .o_we_customize_panel we-toggler.active{position: relative;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active::after, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button::after, #oe_snippets > .o_we_customize_panel we-toggler::after, #oe_snippets > .o_we_customize_panel we-toggler.active::after{position: absolute; top: 50%; left: auto; bottom: auto; right: 0.5em; transform: translateY(-50%); width: 1em; text-align: center; font-family: FontAwesome;}#oe_snippets > .o_we_customize_panel we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-label{display: block; text-transform: capitalize;}#oe_snippets > .o_we_customize_panel we-customizeblock-options{position: relative; display: block; padding: 0 0 15px 0; background-color: #3e3e46; box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.8);}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: 3px 10px 0 15px; background-color: #2b2b33; box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), inset 0 -1px 0 rgba(0, 0, 0, 0.5); font-size: 13px;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > span, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > span{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; cursor: pointer; color: #FFFFFF !important; line-height: 32px;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: -webkit-box; display: -webkit-flex; display: flex; margin-left: auto; font-size: .9em;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group .oe_snippet_remove, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group .oe_snippet_remove{font-size: 1.2em;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-customizeblock-option, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-customizeblock-option, #oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group #oe-table-options, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group #oe-table-options{display: -webkit-box; display: -webkit-flex; display: flex; padding: 0;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-button{margin-top: 0 !important; margin-left: 3px; padding: 0 3px !important;}#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button.fa, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-button.fa, #oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button.o_we_icon_button, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-button.o_we_icon_button{box-sizing: content-box; width: 1.29em; padding: 0 0.15em !important; margin-left: 6px; text-align: center; justify-content: center;}#oe_snippets > .o_we_customize_panel we-customizeblock-option, #oe_snippets > .o_we_customize_panel #oe-table-options{position: relative; display: block; padding: 0 10px 0 15px;}#oe_snippets > .o_we_customize_panel we-customizeblock-option .dropdown-menu, #oe_snippets > .o_we_customize_panel #oe-table-options .dropdown-menu{position: static !important;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert, #oe_snippets > .o_we_customize_panel #oe-table-options > we-alert{background-color: #6999a8; display: block; padding: 6px;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert we-title, #oe_snippets > .o_we_customize_panel #oe-table-options > we-alert we-title, #oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-option > we-alert .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options > we-alert .oe-table-label{margin-bottom: 6px; text-transform: uppercase; font-weight: bold;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-option > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options > .oe-table-label{margin-bottom: -4px; font-size: 13px; color: #FFFFFF; font-weight: 500;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-title:not(:first-child), #oe_snippets > .o_we_customize_panel #oe-table-options > we-title:not(:first-child), #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-option > .oe-table-label:not(:first-child), #oe_snippets > .o_we_customize_panel #oe-table-options > .oe-table-label:not(:first-child){margin-top: 16px;}#oe_snippets > .o_we_customize_panel .o_we_fold_icon{position: absolute; top: 0; left: -15px; bottom: 0; right: 100%; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; align-items: center; width: 15px;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget{margin-top: 8px;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > div{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; min-height: 22px;}#oe_snippets > .o_we_customize_panel .o_we_large > div{flex: 1 1 auto !important; width: 100%;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div{display: block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; min-height: 20px;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div, #oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > .fa{line-height: 20px;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > img{margin-bottom: 1px;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > svg{margin-bottom: 2px;}#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget.fa > div{display: none;}#oe_snippets > .o_we_customize_panel we-button.o_we_icon_button, #oe_snippets > .o_we_customize_panel we-button.fa{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}#oe_snippets > .o_we_customize_panel we-button.fa-fw{padding: 0 .5em; width: 2.29em; justify-content: center;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget{min-width: 20px; padding: 0; border: none; background: none; cursor: default;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options we-button.o_we_checkbox_wrapper.o_we_user_value_widget > .oe-table-label{cursor: pointer;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget > div{display: -webkit-box; display: -webkit-flex; display: flex; min-height: 22px; line-height: 22px;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget we-checkbox{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; width: 20px; height: 12px; background-color: #9d9d9d; border-radius: 10rem; cursor: pointer;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget we-checkbox::after{content: ""; display: block; width: 11px; height: 10px; border-radius: 10rem; background-color: #FFFFFF; box-shadow: 0 2px 3px 0 #000000;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget.active we-checkbox{background-color: #01bad2; -webkit-box-pack: end; justify-content: flex-end;}#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget.active, #oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget:hover{color: #D9D9D9;}#oe_snippets > .o_we_customize_panel we-selection-items .o_we_user_value_widget{margin-top: 0;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget{position: relative;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_icon_select) we-toggler{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 157.8px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret{position: relative; display: block; align-self: flex-end;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::after{content: ''; position: absolute; top: 100%; left: auto; bottom: auto; right: 2em; z-index: 1001; transform: translateX(50%); margin-top: 2px; border-bottom: 7px solid #000000; border-left: 8px solid transparent; border-right: 8px solid transparent;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::after{border-bottom-color: #595964; border-left-width: 7px; border-right-width: 7px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_so_color_palette) + we-button:not(:hover){background: none;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-toggler:empty::before{content: '/';}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items{position: absolute; top: 100%; left: 0; bottom: auto; right: 0; z-index: 1000;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:not(.dropdown-menu){margin-top: 8px !important;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:not(.o_we_has_pager){max-height: 600px; overflow-y: auto;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:empty{line-height: 34px; background-color: #595964; color: #C6C6C6; padding-left: 2em;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:empty::before{content: '/';}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > .oe-table-label{line-height: 34px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button{padding-left: 2em; border: none; background: none; background-clip: padding-box; background-color: #595964; color: #C6C6C6; border-radius: 0;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button::after{content: ""; color: #9d9d9d; right: auto; left: 0.5em;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label{flex-grow: 1;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > div, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label{line-height: 34px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > div img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > div svg, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label img, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title svg, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label svg, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label svg{max-width: 100%;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button:not(.d-none) ~ we-button{border-top: 1px solid transparent;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button:hover{background-color: #2b2b33; color: #D9D9D9;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active{padding-left: 2em; background-color: #42424c; color: #FFFFFF;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active::after{content: ""; color: #9d9d9d; right: auto; left: 0.5em;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active:after{color: #01bad2;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_header{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: space-between; background-color: #595964; margin-bottom: 1px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_header > b{padding: 6px; color: #FFFFFF;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_controls{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_controls > span{margin: 0 6px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_next, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget .o_we_pager_prev{margin: 0.3em; padding: 6px; cursor: pointer; border: 1px solid currentColor; border-radius: 2px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page{display: none; width: 100%; max-height: 562.5px; overflow-y: auto;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page.active{display: block;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-select-page we-button img{height: 60px; width: 80px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-select-page.active{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-select-page.active we-button{width: 33.33333333%; padding: 0; justify-content: center;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-select-page.active we-button img{padding: 8px;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-select-page.active we-button.active{border: 2px solid #40ad67 !important;}#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid.o_we_fake_transparent_background we-button{background-image: url(/web/static/img/transparent.png); background-size: 10px auto;}#oe_snippets > .o_we_customize_panel we-button.o_we_image_shape_remove div{display: -webkit-box; display: -webkit-flex; display: flex;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items{display: -webkit-box; display: -webkit-flex; display: flex; max-width: 100%;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button{padding: 0 6px; border-radius: 0;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button + we-button{border-left: none;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button:first-child, #oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button .active:first-child{border-top-left-radius: 2px; border-bottom-left-radius: 2px;}#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button:last-child, #oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button .active:last-child{border-top-right-radius: 2px; border-bottom-right-radius: 2px;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-button-group.o_we_user_value_widget we-selection-items, #oe_snippets > .o_we_customize_panel #oe-table-options > we-button-group.o_we_user_value_widget we-selection-items{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 157.8px;}#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-button-group.o_we_user_value_widget we-selection-items we-button, #oe_snippets > .o_we_customize_panel #oe-table-options > we-button-group.o_we_user_value_widget we-selection-items we-button{display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; flex: 0 1 25%; padding: 1.5px 2px; text-align: center;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div{-webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto; width: 60px;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget input{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 0; min-width: 2ch; height: 20px; font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget input::placeholder{color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget span{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; padding-right: 6px; font-size: 11px; color: #9d9d9d;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: block; width: 20px; height: 20px; border: 1px solid #000000; border-radius: 10rem; cursor: pointer;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::after{box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5);}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_color_preview{border: 2px solid #01bad2;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::after{right: 10px;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_dropdown_caret::after{border-bottom-width: 8px;}#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget we-toggler{display: none;}#oe_snippets > .o_we_customize_panel we-matrix{overflow-y: auto;}#oe_snippets > .o_we_customize_panel we-matrix table{table-layout: fixed; width: 100%;}#oe_snippets > .o_we_customize_panel we-matrix table td, #oe_snippets > .o_we_customize_panel we-matrix table th{text-align: center;}#oe_snippets > .o_we_customize_panel we-matrix table td we-button, #oe_snippets > .o_we_customize_panel we-matrix table th we-button{display: inline-block; color: inherit; height: 100%;}#oe_snippets > .o_we_customize_panel we-matrix table td we-button.o_we_matrix_remove_col, #oe_snippets > .o_we_customize_panel we-matrix table td we-button.o_we_matrix_remove_row, #oe_snippets > .o_we_customize_panel we-matrix table th we-button.o_we_matrix_remove_col, #oe_snippets > .o_we_customize_panel we-matrix table th we-button.o_we_matrix_remove_row{display: none;}#oe_snippets > .o_we_customize_panel we-matrix table td input, #oe_snippets > .o_we_customize_panel we-matrix table th input{border: 1px solid #000000; background-color: #2b2b33; color: inherit; font-size: 12px; width: 100%;}#oe_snippets > .o_we_customize_panel we-matrix table td:last-child, #oe_snippets > .o_we_customize_panel we-matrix table th:last-child{width: 28px;}#oe_snippets > .o_we_customize_panel we-matrix table tr:last-child we-button{overflow: visible;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget[data-display-range-value] input[type="range"]{min-width: 0;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 157.8px; height: 22px; padding: 0 1px 0 0; background-color: transparent; -webkit-appearance: none; -moz-appearance: none; appearance: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]:focus{outline: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]:focus::-webkit-slider-thumb{box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]:focus::-moz-range-thumb{box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]:focus::-ms-thumb{box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-focus-outer{border: 0;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-webkit-slider-thumb{width: 10px; height: 10px; margin-top: -3px; border: none; border-radius: 10rem; background-color: #01bad2; box-shadow: none; -webkit-appearance: none; -moz-appearance: none; appearance: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-webkit-slider-thumb:active{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-webkit-slider-runnable-track{width: 100%; height: 4px; cursor: pointer; background-color: #9d9d9d; border-color: transparent; border-radius: 10rem; box-shadow: none; position: relative;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-range-thumb{width: 10px; height: 10px; border: none; border-radius: 10rem; background-color: #01bad2; box-shadow: none; -webkit-appearance: none; -moz-appearance: none; appearance: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-range-thumb:active{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-range-track{width: 100%; height: 4px; cursor: pointer; background-color: #9d9d9d; border-color: transparent; border-radius: 10rem; box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-moz-range-progress{background-color: #01bad2; height: 4px; border-color: transparent; border-radius: 10rem;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-thumb{width: 10px; height: 10px; margin-top: 0; margin-right: 0; margin-left: 0; border: none; border-radius: 10rem; background-color: #01bad2; box-shadow: none; -webkit-appearance: none; -moz-appearance: none; appearance: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-thumb:active{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-track{width: 100%; height: 4px; cursor: pointer; background-color: transparent; border-color: transparent; border-width: 5px; box-shadow: none;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-fill-lower{background-color: #01bad2; border-radius: 10rem; border-radius: 1rem;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"]::-ms-fill-upper{background-color: #9d9d9d; border-radius: 10rem;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range{transform: rotate(180deg);}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range::-moz-range-track{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range::-moz-range-progress{background-color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range::-ms-fill-lower{background-color: #9d9d9d;}#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type="range"].o_we_inverted_range::-ms-fill-upper{background-color: #01bad2;}#oe_snippets > .o_we_customize_panel we-list > div{-webkit-flex-flow: row wrap; flex-flow: row wrap;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper{width: 100%; max-height: 200px; overflow-y: auto; margin-bottom: 4px;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table{table-layout: auto; width: 100%;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table input::-webkit-outer-spin-button, #oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table input::-webkit-inner-spin-button{-webkit--webkit-appearance: none; -moz-appearance: none; appearance: none; margin: 0;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table input[type=number]{-moz--webkit-appearance: textfield; -moz-appearance: textfield; appearance: textfield;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table input{width: 100%; border: 1px solid #000000; border-radius: 2px; padding: 0 6px; background-color: #2b2b33; color: inherit; font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table tr{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; border: none;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table td{flex-grow: 1; padding-bottom: 4px;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table td:not(.o_we_list_record_name){flex-grow: 0;}#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table td we-button.o_we_checkbox_wrapper{margin: 0 0 0 0.3em;}#oe_snippets > .o_we_customize_panel we-list .o_we_user_value_widget{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget{margin-top: 0;}#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget > div{-webkit-flex-flow: row wrap; flex-flow: row wrap;}#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget > div > *{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#oe_snippets > .o_we_customize_panel .o_we_m2o_search{background-color: #595964; flex-grow: 1 !important; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; margin-bottom: 1px; border-radius: 2px; padding: .25em .5em;}#oe_snippets > .o_we_customize_panel .o_we_m2o_search::before{content: "\f002"; font-size: 1.2em; padding-right: .5em; font-family: FontAwesome;}#oe_snippets > .o_we_customize_panel .o_we_m2o_search input{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; color: inherit; border: 1px solid #000000; border-radius: 2px; background-color: #2b2b33; padding: 1px 6px;}#oe_snippets > .o_we_customize_panel .o_we_m2o_search input:focus{outline: none; border-color: #01bad2;}#oe_snippets > .o_we_customize_panel .o_we_m2o_search input::placeholder{color: #9d9d9d;}#oe_snippets > .o_we_customize_panel .o_we_m2o_search_more{color: var(--o-cc1-btn-primary); margin-top: 1px; width: 100%; cursor: pointer; padding-left: 2em; line-height: 20px;}#oe_snippets > .o_we_customize_panel .o_we_m2o_create{margin-top: 1px;}#oe_snippets > .o_we_customize_panel .o_we_m2m we-list, #oe_snippets > .o_we_customize_panel .o_we_m2m we-list > div, #oe_snippets > .o_we_customize_panel .o_we_m2m we-list we-select{margin-top: 0; max-width: 100%;}#oe_snippets > .o_we_customize_panel we-row, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row{position: relative; margin-top: 8px;}#oe_snippets > .o_we_customize_panel we-row .o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_we_user_value_widget{margin-top: 0; min-width: 4em;}#oe_snippets > .o_we_customize_panel we-row we-button.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row we-button.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row .o_we_so_color_palette.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_we_so_color_palette.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row we-button-group.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row we-button-group.o_we_user_value_widget{min-width: auto;}#oe_snippets > .o_we_customize_panel we-row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center;}#oe_snippets > .o_we_customize_panel we-row > div > :not(.d-none) ~ *, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div > :not(.d-none) ~ *{margin-left: 3px;}#oe_snippets > .o_we_customize_panel we-row we-select.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row we-select.o_we_user_value_widget{position: static;}#oe_snippets > .o_we_customize_panel we-row.o_we_full_row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_full_row.oe-table-row > div{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#oe_snippets > .o_we_customize_panel we-row.o_short_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_short_title.oe-table-row we-title, #oe_snippets > .o_we_customize_panel we-row.o_short_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row.o_short_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_short_title.oe-table-row .oe-table-label, #oe_snippets > .o_we_customize_panel we-row .o_short_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_short_title we-title, #oe_snippets > .o_we_customize_panel we-row .o_short_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row .o_short_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_short_title .oe-table-label{width: unset !important; padding-right: 0 !important;}#oe_snippets > .o_we_customize_panel we-row.o_long_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_long_title.oe-table-row we-title, #oe_snippets > .o_we_customize_panel we-row.o_long_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row.o_long_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_long_title.oe-table-row .oe-table-label, #oe_snippets > .o_we_customize_panel we-row .o_long_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_long_title we-title, #oe_snippets > .o_we_customize_panel we-row .o_long_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row .o_long_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_long_title .oe-table-label{width: fit-content !important; padding-right: 10px !important;}#oe_snippets > .o_we_customize_panel we-row.o_design_tab_title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_design_tab_title.oe-table-row{margin-top: 15px;}#oe_snippets > .o_we_customize_panel we-row.o_design_tab_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_design_tab_title.oe-table-row we-title, #oe_snippets > .o_we_customize_panel we-row.o_design_tab_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row.o_design_tab_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_design_tab_title.oe-table-row .oe-table-label{font-weight: 600;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row wrap; flex-flow: row wrap;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > we-title, #oe_snippets > .o_we_customize_panel we-row > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_user_value_widget > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > .oe-table-label{width: 100%;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > div, #oe_snippets > .o_we_customize_panel we-row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div{-webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto; min-width: 0; margin-top: 8px;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw), #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw){-webkit-flex-flow: row nowrap; flex-flow: row nowrap; align-items: center;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw) > we-title, #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw) > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_user_value_widget:not(.o_we_fw) > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row:not(.o_we_fw) > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) > .oe-table-label{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; width: 105.2px; padding-right: 6px;}#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw) > div, #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw) > div, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) > div{margin-top: 0;}#oe_snippets > .o_we_customize_panel we-collapse{position: relative; display: block; padding-left: 15px; padding-right: 10px; margin-right: -10px; margin-left: -15px; border-top: 4px solid transparent; padding-bottom: 4px; margin-bottom: -4px; background-clip: padding-box;}#oe_snippets > .o_we_customize_panel we-collapse > :first-child, #oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler{margin-top: 4px;}#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler{position: absolute; top: 0; left: 0; bottom: auto; right: auto; width: 15px; height: 22px; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center; padding: 0; background: none; border: none;}#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler::after{content: '\f0da'; position: static; transform: none;}#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler.active::after{content: '\f0d7';}#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler.active + *{background: none; border: none; box-shadow: none;}#oe_snippets > .o_we_customize_panel we-collapse.active{box-shadow: inset 0 1px 0 rgba(0, 0, 0, 0.5), inset 0 -1px 0 rgba(255, 255, 255, 0.2);}#oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active, #oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active we-toggler.o_we_collapse_toggler{background-color: #3e3e46;}#oe_snippets > .o_we_customize_panel we-collapse.active, #oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active we-collapse.active, #oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active we-collapse.active .o_we_collapse_toggler{background-color: #2b2b33;}#oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler{cursor: pointer;}#oe_snippets > .o_we_customize_panel .o_we_sublevel_3 > we-title::before, #oe_snippets > .o_we_customize_panel .o_we_sublevel_2 > we-title::before, #oe_snippets > .o_we_customize_panel .o_we_sublevel_1 > we-title::before, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_3 > .oe-table-label::before, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_2 > .oe-table-label::before, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_1 > .oe-table-label::before{content: "└"; display: inline-block; margin-right: 0.4em;}#oe_snippets > .o_we_customize_panel .o_we_sublevel_2 > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_2 > .oe-table-label{padding-left: 0.6em;}#oe_snippets > .o_we_customize_panel .o_we_sublevel_3 > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_3 > .oe-table-label{padding-left: 1.2em;}#oe_snippets > .o_we_customize_panel .o_we_image_weight{margin-left: 12px;}#oe_snippets > .o_we_customize_panel .o_we_tag{background-color: #000000; white-space: nowrap; padding: 1.5px 3px; border-radius: 3px; font-size: 0.85em;}#oe_snippets > .o_we_invisible_el_panel{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; max-height: 220px; overflow-y: auto; margin-top: auto; padding: 10px; background-color: #191922; box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);}#oe_snippets > .o_we_invisible_el_panel .o_panel_header{padding: 8px 0;}#oe_snippets > .o_we_invisible_el_panel .o_we_invisible_entry{padding: 8px 6px; cursor: pointer;}#oe_snippets > .o_we_invisible_el_panel .o_we_invisible_entry:hover{background-color: #2b2b33;}#oe_snippets.o_we_backdrop > .o_we_customize_panel{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}#oe_snippets.o_we_backdrop > .o_we_customize_panel::after{content: ""; position: -webkit-sticky; position: sticky; top: auto; left: 0; bottom: 0; right: 0; display: block; height: 100vh; margin-top: -100vh; pointer-events: none; background: rgba(0, 0, 0, 0.2);}#oe_snippets.o_we_backdrop .o_we_widget_opened{z-index: 1000;}.o_we_cc_preview_wrapper{font-family: sans-serif !important; font-size: 15px !important; padding: 8px 8px 6.4px;}.o_we_cc_preview_wrapper > *{margin-bottom: 0 !important; line-height: 1 !important;}.o_we_color_combination_btn_text{color: inherit !important; font-family: inherit !important; font-size: 0.8em !important; margin-top: 0.5em !important;}.o_we_color_combination_btn_title{margin-top: 0 !important; font-size: 1.3em !important;}.o_we_color_combination_btn_btn{padding: 0.2em 3px 0.3em !important; border-radius: 2px !important; font-size: 0.8em !important;}.o_we_border_preview{display: inline-block; width: 999px; max-width: 100%; margin-bottom: 2px; border-width: 4px; border-bottom: none !important;}#oe_snippets .colorpicker{--bg: #3e3e46; --text-rgb: 217, 217, 217; --border-rgb: var(--text-rgb); --tab-border-top: rgba(255, 255, 255, .2); --tab-border-bottom: #191922; --btn-color-active: inset 0 0 0 1px #3e3e46, inset 0 0 0 3px #01bad2, inset 0 0 0 4px white;}#oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn{-webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto;}#oe_snippets .colorpicker .o_colorpicker_reset{border: 0; background-color: transparent;}#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn{float: none; width: 100%; padding: 0; margin: 0; border: 0; background-color: transparent; background-clip: padding-box; border-top: 8px solid transparent; border-bottom: 8px solid transparent;}#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn + .o_we_color_combination_btn{margin-top: -4px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn.selected > .o_we_cc_preview_wrapper{box-shadow: 0 0 0 1px #40ad67 !important;}#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn.selected .o_we_color_combination_btn_title::before{content: "\f00c"; margin-right: 8px; font-size: 0.8em; font-family: FontAwesome; color: #40ad67;}#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn .o_we_cc_preview_wrapper:after{bottom: -1px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor{font-size: 12px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_btn{color: #ffffff; background-color: #3e3e46; float: none; box-sizing: border-box;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor span.o_custom_gradient_input{border: 1px solid black;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor span.o_custom_gradient_input input{outline: none;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor span.o_custom_gradient_input:focus-within{border-color: #01bad2;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale{cursor: copy;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale div{height: 20px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi{display: grid;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]{pointer-events: none; focusable: false; grid-column: 1/span 2; grid-row: 3; background: none; -webkit-appearance: none; -moz-appearance: none; appearance: none; cursor: ew-resize;}@supports (-moz-appearance: none){#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]{margin-top: 2px;}}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]::-webkit-slider-thumb{pointer-events: auto; border: 1.5px solid rgba(255, 255, 255, 0.8); background: currentColor; -webkit-appearance: none; -moz-appearance: none; appearance: none; box-shadow: 0px 0px 0px #000000; height: 20px; width: 12px; border-radius: 5px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range].active::-webkit-slider-thumb{border-color: #01bad2;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]::-moz-range-thumb{pointer-events: auto; border: 1.5px solid rgba(255, 255, 255, 0.8); background: currentColor; box-shadow: 0px 0px 0px #000000; height: 18px; width: 10px; border-radius: 5px; margin-top: 3px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range].active::-moz-range-thumb{border-color: #01bad2;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]::-ms-thumb{pointer-events: auto; border: 1.5px solid rgba(255, 255, 255, 0.8); background: currentColor; box-shadow: 0px 0px 0px #000000; height: 20px; width: 12px; border-radius: 5px;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range].active::-ms-thumb{border-color: #01bad2;}#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_remove_color{font-size: 14px !important; text-align: center !important; padding: 0;}@keyframes dropZoneInsert{to{background-color: rgba(113, 75, 103, 0.3);}}.oe_drop_zone{background-color: rgba(113, 75, 103, 0.15); animation: dropZoneInsert 1s linear 0s infinite alternate;}.oe_drop_zone.oe_insert{position: relative; z-index: 1040; width: 100%; border: 2px dashed #2b2b33;}.oe_drop_zone:not(.oe_grid_zone).oe_insert{min-width: 30px; height: 30px; min-height: 30px; margin: -15px 0; padding: 0;}.oe_drop_zone:not(.oe_grid_zone).oe_insert.oe_vertical{width: 30px; float: left; margin: 0 -15px;}.oe_drop_zone:not(.oe_grid_zone).oe_drop_zone_danger{background-color: rgba(230, 88, 108, 0.15); color: #e6586c; border-color: #e6586c;}#oe_manipulators{position: relative; z-index: 1040; pointer-events: none;}#oe_manipulators .oe_overlay{position: absolute; top: auto; left: auto; bottom: auto; right: auto; display: none; border-color: #01bad2; background: transparent; text-align: center; font-size: 16px; transition: opacity 400ms linear 0s;}#oe_manipulators .oe_overlay.o_overlay_hidden{opacity: 0; transition: none;}#oe_manipulators .oe_overlay.oe_active{display: block; z-index: 1;}#oe_manipulators .oe_overlay > .o_handles{position: absolute; top: -10000px; left: 0; bottom: auto; right: 0; border-color: inherit; pointer-events: auto;}#oe_manipulators .oe_overlay > .o_handles:hover > .o_handle{background-color: rgba(1, 186, 210, 0.05);}#oe_manipulators .oe_overlay > .o_handles > .o_handle{position: relative; border: 0 solid transparent; border-color: inherit; transition: background 300ms ease 0s;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.w{position: absolute; top: 10000px; left: 0; bottom: -10000px; right: auto; width: 8px; border-width: 2px; border-right-width: 0; cursor: e-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.w.o_grid_handle{cursor: ew-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.w:after{position: absolute; top: 50%; left: 40%; bottom: auto; right: auto; margin-top: -10px;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.w.o_grid_handle:after{display: none;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.e{position: absolute; top: 10000px; left: auto; bottom: -10000px; right: 0; width: 8px; border-right-width: 2px; cursor: w-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.e.o_grid_handle{cursor: ew-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.e:after{position: absolute; top: 50%; left: auto; bottom: auto; right: 40%; margin-top: -10px;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.e.o_grid_handle:after{display: none;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.n{position: absolute; top: 10000px; left: 0; bottom: auto; right: 0; height: 8px; border-top-width: 2px; cursor: ns-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.n:after{position: absolute; top: 40%; left: 50%; bottom: auto; right: auto; margin-left: -10px;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.n.o_grid_handle:after{display: none;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.s{position: absolute; top: auto; left: 0; bottom: -10000px; right: 0; height: 8px; border-bottom-width: 2px; cursor: ns-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.s:after{position: absolute; top: auto; left: 50%; bottom: 40%; right: auto; margin-left: -10px;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.s.o_grid_handle:after{display: none;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.ne{position: absolute; top: 10002px; left: auto; bottom: auto; right: 2px; height: 8px; cursor: nesw-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.se{position: absolute; top: auto; left: auto; bottom: -10002px; right: 2px; height: 8px; cursor: nwse-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.se:after{position: absolute; top: 50%; left: auto; bottom: auto; right: 40%;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.sw{position: absolute; top: auto; left: 2px; bottom: -10001px; right: auto; height: 8px; cursor: nesw-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.sw:after{position: absolute; top: auto; left: 40%; bottom: 40%; right: auto;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.nw{position: absolute; top: 9998px; left: 3px; bottom: auto; right: auto; height: 8px; cursor: nwse-resize;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.nw:after{position: absolute; top: auto; left: 50%; bottom: 40%; right: auto;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.ne, #oe_manipulators .oe_overlay > .o_handles > .o_handle.sw{content: "\f065";}#oe_manipulators .oe_overlay > .o_handles > .o_handle.nw, #oe_manipulators .oe_overlay > .o_handles > .o_handle.se{content: "\f065"; transform: rotate(90deg);}#oe_manipulators .oe_overlay > .o_handles > .o_handle::after{z-index: 1; display: block; width: 20px; height: 20px; border: solid 1px #01606c; line-height: 18px; font-size: 14px; font-family: FontAwesome; background-color: #018d9f; color: white;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start:after{background-color: rgba(89, 89, 100, 0.6); border-color: rgba(0, 0, 0, 0.2);}#oe_manipulators .oe_overlay > .o_handles > .o_handle:hover, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_active{background-color: rgba(1, 186, 210, 0.2);}#oe_manipulators .oe_overlay > .o_handles > .o_handle:hover::after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_active::after{border-color: #018d9f; background-color: #01606c;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.w:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.e:after{content: "\f07e";}#oe_manipulators .oe_overlay > .o_handles > .o_handle.s:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.n:after{content: "\f07d";}#oe_manipulators .oe_overlay > .o_handles > .o_handle.ne:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.sw:after{content: "\f065";}#oe_manipulators .oe_overlay > .o_handles > .o_handle.nw:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.se:after{content: "\f065"; transform: rotate(180deg);}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start.w:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start.e:after{content: '\f061';}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start.n:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_start.s:after{content: '\f063';}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_end.w:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_end.e:after{content: '\f060';}#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_end.n:after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.o_handle_end.s:after{content: '\f062';}#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly{cursor: auto !important;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly:after{display: none !important;}#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly:hover{opacity: 0.5;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap{position: absolute; top: 10000px; left: 50%; bottom: auto; right: auto; transform: translate(-50%, -110%);}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap.o_we_hidden_overlay_options{display: none;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options{display: -webkit-box; display: -webkit-flex; display: flex;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button{margin: 0 1px 0; min-width: 22px; padding: 0 3px; color: #FFFFFF;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *.fa-trash, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *.fa-trash, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.fa-trash{background-color: #a05968;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *.fa-trash:not(.oe_snippet_remove), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *.fa-trash:not(.oe_snippet_remove), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.fa-trash:not(.oe_snippet_remove){opacity: 0.5;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .o_move_handle{cursor: move; width: 30px; height: 22px; background-image: url("/web_editor/static/src/img/snippets_options/o_overlay_move_drag.svg"); background-position: center; background-repeat: no-repeat;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .o_send_back{width: 30px; height: 22px; background-image: url("/web_editor/static/src/img/snippets_options/bring-backward.svg"); background-position: center; background-repeat: no-repeat;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .o_bring_front{width: 30px; height: 22px; background-image: url("/web_editor/static/src/img/snippets_options/bring-forward.svg"); background-position: center; background-repeat: no-repeat;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button{opacity: 0.6;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *.focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *.focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button.focus{opacity: 1;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:hover{border-color: #2b2c34; background-color: #2b2b33;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:hover.fa-trash, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:hover.fa-trash, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:hover.fa-trash{border-color: #2c2b33; background-color: #e6586c;}#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:hover.fa-trash:not(.oe_snippet_remove), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:hover.fa-trash:not(.oe_snippet_remove), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:hover.fa-trash:not(.oe_snippet_remove){opacity: 0.5;}#oe_manipulators .oe_overlay.o_top_cover > .o_handles > .o_overlay_options_wrap{top: auto; bottom: -10000px; transform: translate(-50%, 110%);}#oe_manipulators .oe_overlay.o_we_overlay_preview{pointer-events: none;}#oe_manipulators .oe_overlay.o_we_overlay_preview > .o_handles > .o_handle::after, #oe_manipulators .oe_overlay.o_we_overlay_preview > .o_handles .o_overlay_options_wrap{display: none;}#oe_manipulators .oe_overlay.o_we_background_position_overlay{background-color: rgba(0, 0, 0, 0.7); pointer-events: auto;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_content{cursor: url(/web/static/img/openhand.cur), grab;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_content .o_we_grabbing{cursor: grabbing;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary{color: #FFFFFF; background-color: #017e84; border-color: #017e84;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:hover{color: #FFFFFF; background-color: #016b70; border-color: #01656a;}.btn-check:focus + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:focus{color: #FFFFFF; background-color: #016b70; border-color: #01656a; box-shadow: 0 0 0 0.25rem rgba(39, 145, 150, 0.5);}.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:active, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.active, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.dropdown-toggle{color: #FFFFFF; background-color: #01656a; border-color: #015f63;}.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:focus, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:active:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.active:focus, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(39, 145, 150, 0.5);}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:disabled, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.disabled{color: #FFFFFF; background-color: #017e84; border-color: #017e84;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary{color: #000000; background-color: #e6586c; border-color: #e6586c;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:hover{color: #000000; background-color: #ea7182; border-color: #e9697b;}.btn-check:focus + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:focus{color: #000000; background-color: #ea7182; border-color: #e9697b; box-shadow: 0 0 0 0.25rem rgba(196, 75, 92, 0.5);}.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:active, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.active, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.dropdown-toggle{color: #000000; background-color: #eb7989; border-color: #e9697b;}.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:focus, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:active:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.active:focus, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(196, 75, 92, 0.5);}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:disabled, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.disabled{color: #000000; background-color: #e6586c; border-color: #e6586c;}#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_overlay_background > *{display: block !important; top: 0 !important; right: 0 !important; bottom: 0 !important; left: 0 !important; transform: none !important; max-width: unset !important; max-height: unset !important;}#oe_manipulators .o_edit_menu_popover{pointer-events: auto;}.oe_overlay.ui-draggable-dragging .o_handles{display: none;}.s-resize-important *{cursor: s-resize !important;}.n-resize-important *{cursor: n-resize !important;}.e-resize-important *{cursor: e-resize !important;}.w-resize-important *{cursor: w-resize !important;}.move-important *{cursor: move !important;}.dropdown-menu label .o_switch{margin: 0; padding: 2px 0;}.text-input-group{position: relative; margin-bottom: 45px;}.text-input-group input{font-size: 18px; padding: 10px 10px 10px 5px; display: block; width: 300px; border: none; border-bottom: 1px solid #757575;}.text-input-group input:focus{outline: none;}.text-input-group label{color: #999; font-size: 18px; font-weight: normal; position: absolute; top: 10px; left: 5px; bottom: auto; right: auto; pointer-events: none; transition: 0.2s ease all;}.text-input-group input:focus ~ label, .text-input-group input:valid ~ label{top: -20px; font-size: 14px; color: #5264AE;}.text-input-group .bar{position: relative; display: block; width: 300px;}.text-input-group .bar:before, .text-input-group .bar:after{content: ''; height: 2px; width: 0; bottom: 1px; position: absolute; top: auto; left: auto; bottom: auto; right: auto; background: #5264AE; transition: 0.2s ease all;}.text-input-group .bar:before{left: 50%;}.text-input-group .bar:after{right: 50%;}.text-input-group input:focus ~ .bar:before, .text-input-group input:focus ~ .bar:after{width: 50%;}.text-input-group .highlight{position: absolute; top: 25%; left: 0; bottom: auto; right: auto; height: 60%; width: 100px; pointer-events: none; opacity: 0.5;}.text-input-group input:focus ~ .highlight{animation: inputHighlighter 0.3s ease;}.oe_snippet_body{opacity: 0; animation: fadeInDownSmall 700ms forwards;}.o_container_preview{outline: 2px dashed #01bad2;}we-select.o_we_shape_menu we-button[data-shape]{padding: 0 !important;}we-select.o_we_shape_menu we-button[data-shape].active{border: 1px solid #40ad67 !important;}we-select.o_we_shape_menu we-button[data-shape] div{width: 100%;}we-select.o_we_shape_menu we-button[data-shape] .o_we_shape_btn_content{width: 100%; height: 75px;}.o_we_shape_animated_label{position: absolute; top: 0; left: auto; bottom: auto; right: 0; padding: 0 6px; background: #40ad67; color: white;}.o_we_shape_animated_label > span{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; max-width: 0;}we-button[data-img-size]{position: relative;}we-button[data-img-size]::before, we-button[data-img-size]::after{padding: 0 6px; background: #40ad67; color: white; text-align: center;}we-button[data-img-size]::before{font-family: "FontAwesome" !important; content: "\f065"; position: absolute; top: 0; left: inherit; bottom: inherit; right: 0;}we-button[data-img-size]::after{content: attr(data-img-size); position: absolute; top: 0; left: 0; bottom: inherit; right: 0; transform: translateX(100%); transition: 0.2s ease all;}we-button[data-img-size]:hover::after{transform: translateX(0%);}we-button:hover .o_we_shape_animated_label > span{max-width: 150px; transition: max-width 0.5s ease 0s;}.o_we_ui_loading{position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: 1041; background-color: rgba(0, 0, 0, 0.2); color: #FFFFFF;}#oe_manipulators > .o_we_ui_loading{position: fixed;}.o_we_force_no_transition{transition: none !important;}we-button.o_grid{min-width: fit-content; padding-left: 3px !important; padding-right: 3px !important;}we-select.o_grid we-toggler{width: fit-content !important;}we-button-group.o_grid{min-width: fit-content !important;}.o_we_background_grid{padding: 0 !important;}.o_we_background_grid .o_we_cell{fill: #714B67; fill-opacity: .2; stroke: #2b2b33; stroke-width: 1px; filter: drop-shadow(-1px -1px 0px rgba(255, 255, 255, 0.6));}.o_we_drag_helper{padding: 0; border: 2px solid #01bad2;}@keyframes highlightPadding{from{border: solid rgba(1, 186, 210, 0.2); border-width: var(--grid-item-padding-y) var(--grid-item-padding-x);}to{border: solid rgba(1, 186, 210, 0); border-width: var(--grid-item-padding-y) var(--grid-item-padding-x);}}.o_we_padding_highlight > .o_grid_item{position: relative;}.o_we_padding_highlight > .o_grid_item::after{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; animation: highlightPadding 2s; pointer-events: none;}