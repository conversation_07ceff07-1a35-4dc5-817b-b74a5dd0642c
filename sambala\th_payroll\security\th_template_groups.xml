<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <!--======================= Nhân viên : <PERSON><PERSON> bộ =======================-->
        <record id="th_employee.th_template_employee_user" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('th_access_th_payroll_user'))]"/>
        </record>

        <record model="ir.rule" id="th_template_th_employee_hr_rule_parameter_user">
            <field name="name">th_template_th_employee_hr_rule_parameter_user</field>
            <field name="model_id" ref="hr_payroll.model_hr_rule_parameter"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
            <field name="groups" eval="[(4, ref('th_employee.th_template_employee_user'))]"/>
        </record>

        <record model="ir.rule" id="th_template_th_employee_hr_rule_parameter_value_user">
            <field name="name">th_template_th_employee_hr_rule_parameter_value_user</field>
            <field name="model_id" ref="hr_payroll.model_hr_rule_parameter"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
            <field name="groups" eval="[(4, ref('th_employee.th_template_employee_user'))]"/>
        </record>

        <record model="ir.rule" id="th_template_th_employee_hr_work_entry_type_user">
            <field name="name">th_template_th_employee_hr_work_entry_type_user</field>
            <field name="model_id" ref="model_hr_work_entry_type"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
            <field name="groups" eval="[(4, ref('th_employee.th_template_employee_user'))]"/>
        </record>

        <record model="ir.rule" id="th_template_th_employee_hr_work_entry_user">
            <field name="name">th_template_th_employee_hr_work_entry_user</field>
            <field name="model_id" ref="model_hr_work_entry"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
            <field name="groups" eval="[(4, ref('th_employee.th_template_employee_user'))]"/>
        </record>

        <record model="ir.rule" id="th_template_th_employee_hr_payroll_structure_user">
            <field name="name">th_template_th_employee_hr_payroll_structure_user</field>
            <field name="model_id" ref="model_hr_payroll_structure"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
            <field name="groups" eval="[(4, ref('th_employee.th_template_employee_user'))]"/>
        </record>

        <!--======================= Nhân viên : Người quản trị =======================-->
        <record id="th_employee.th_template_employee_manager" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('th_access_th_payroll_manager'))]"/>
        </record>

        <record model="ir.rule" id="th_template_th_employee_hr_rule_parameter">
            <field name="name">th_template_th_employee_hr_rule_parameter</field>
            <field name="model_id" ref="hr_payroll.model_hr_rule_parameter"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
            <field name="groups" eval="[(4, ref('th_employee.th_template_employee_manager'))]"/>
        </record>

        <record model="ir.rule" id="th_template_th_employee_hr_payroll_structure">
            <field name="name">th_template_th_employee_hr_payroll_structure</field>
            <field name="model_id" ref="model_hr_payroll_structure"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
            <field name="groups" eval="[(4, ref('th_employee.th_template_employee_manager'))]"/>
        </record>

        <record model="ir.rule" id="th_template_th_employee_hr_rule_parameter_value">
            <field name="name">th_template_th_employee_hr_rule_parameter_value</field>
            <field name="model_id" ref="hr_payroll.model_hr_rule_parameter_value"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
            <field name="groups" eval="[(4, ref('th_employee.th_template_employee_manager'))]"/>
        </record>

        <record model="ir.rule" id="th_template_th_employee_hr_work_entry">
            <field name="name">th_template_th_employee_hr_work_entry</field>
            <field name="model_id" ref="model_hr_work_entry"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
            <field name="groups" eval="[(4, ref('th_employee.th_template_employee_manager'))]"/>
        </record>

    </data>
</odoo>
