<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="view_hr_appraisal_form_inherit" model="ir.ui.view">
            <field name="name">view_hr_appraisal_form_inherit</field>
            <field name="model">hr.appraisal</field>
            <field name="inherit_id" ref="hr_appraisal.view_hr_appraisal_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='date_close']" position="before">
                    <field name="hr_appraisal_type_id"/>
                </xpath>
                <xpath expr="//field[@name='manager_feedback']" position="attributes">
                    <attribute name="options">{'codeview': true}</attribute>
                </xpath>
                <xpath expr="//field[@name='employee_feedback']" position="attributes">
                    <attribute name="options">{'codeview': true}</attribute>
                </xpath>
                <!--thêm state quản lý xác nhận-->
                <xpath expr="//field[@name='state']" position="attributes">
                    <attribute name="statusbar_visible">new,pending,done,manager_approved</attribute>
                </xpath>
                <xpath expr="//header" position="inside">
                    <field name="th_check_user" invisible="1"/>
                    <button name="th_action_manager_confirm" type="object" string="QLTT xác nhận"
                            class="oe_highlight"
                            attrs="{'invisible': ['|',('state','!=','pending'),('th_check_user', '=', False)]}"/>
                </xpath>
                <xpath expr="//button[@name='action_done']" position="attributes">
                    <attribute name="attrs">{'invisible': [('state','!=','manager_approved')]}</attribute>
                </xpath>
                <xpath expr="//button[@name='action_cancel']" position="attributes">
                    <attribute name="attrs">{'invisible': [('state','!=','manager_approved'),('state','!=','pending')]}</attribute>
                </xpath>

            </field>
        </record>

    </data>
</odoo>