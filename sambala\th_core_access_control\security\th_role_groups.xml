<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="th_role_admin_aum" model="res.groups">
        <field name="name">Vai trò: Admin</field>
    </record>

    <record id="th_role_employee_tvts" model="res.groups">
        <field name="name">Vai trò: Nhân viên TVTS</field>
        <field name="implied_ids" eval="[(4, ref('th_template_base_user_aum'))]"/>
    </record>

    <record id="th_role_leader_tvts" model="res.groups">
        <field name="name">Vai trò: Trưởng nhóm TVTS</field>
        <field name="implied_ids" eval="[(4, ref('th_role_employee_tvts'))]"/>
    </record>

    <record id="th_role_employee_error_handling" model="res.groups">
        <field name="name">Vai trò: Nhân viên xử lý lỗi HL</field>
    </record>

    <record id="th_role_employee_error_classification" model="res.groups">
        <field name="name">Vai trò: Nhân viên phân loại lỗi HL</field>
    </record>

    <record id="th_role_manager_learning_materials" model="res.groups">
        <field name="name">Vai trò: Quản lý HL</field>
    </record>

    <record id="th_role_leader_learning_materials" model="res.groups">
        <field name="name">Vai trò: Trưởng phòng HL</field>
    </record>

    <record id="th_role_employee_customer_care" model="res.groups">
        <field name="name">Vai trò: Nhân viên CSKH</field>
        <field name="implied_ids" eval="[(4, ref('th_template_base_user_aum'))]"/>
    </record>

    <record id="th_role_leader_customer_care" model="res.groups">
        <field name="name">Vai trò: Trưởng nhóm CSKH</field>
        <field name="implied_ids" eval="[(4, ref('th_role_employee_customer_care'))]"/>
    </record>

    <record id="th_role_employee_accounting" model="res.groups">
        <field name="name">Vai trò: Nhân viên KT</field>
        <field name="implied_ids" eval="[(4, ref('th_template_base_user_aum'))]"/>
    </record>

    <record id="th_role_leader_accounting" model="res.groups">
        <field name="name">Vai trò: Trưởng phòng KT</field>
        <field name="implied_ids" eval="[(4, ref('th_role_employee_accounting'))]"/>
    </record>

    <record id="th_role_employee_human_resources" model="res.groups">
        <field name="name">Vai trò: Nhân viên HCNS</field>
        <field name="implied_ids" eval="[(4, ref('th_template_base_user_aum'))]"/>
    </record>

    <record id="th_role_leader_human_resources" model="res.groups">
        <field name="name">Vai trò: Trưởng phòng HCNS</field>
        <field name="implied_ids" eval="[(4, ref('th_role_employee_human_resources'))]"/>
    </record>

    <record id="th_role_employee_operating" model="res.groups">
        <field name="name">Vai trò: Nhân viên Vận hành</field>
        <field name="implied_ids" eval="[(4, ref('th_template_base_user_aum'))]"/>
    </record>

    <record id="th_role_leader_operating" model="res.groups">
        <field name="name">Vai trò: Trưởng nhóm Vận hành</field>
        <field name="implied_ids" eval="[(4, ref('th_role_employee_operating'))]"/>
    </record>

    <record id="th_role_employee_network_development" model="res.groups">
        <field name="name">Vai trò: Nhân viên PTML</field>
        <field name="implied_ids" eval="[(4, ref('th_template_base_user_aum'))]"/>
    </record>

    <record id="th_role_leader_network_development" model="res.groups">
        <field name="name">Vai trò: Trưởng nhóm PTML</field>
        <field name="implied_ids" eval="[(4, ref('th_role_employee_network_development'))]"/>
    </record>

    <record id="th_role_manager_network_development" model="res.groups">
        <field name="name">Vai trò: Trưởng phòng PTML</field>
        <field name="implied_ids" eval="[(4, ref('th_role_leader_network_development'))]"/>
    </record>

    <record id="th_role_employee_marketing" model="res.groups">
        <field name="name">Vai trò: Nhân viên MKT</field>
        <field name="implied_ids" eval="[(4, ref('th_template_base_user_aum'))]"/>
    </record>

    <record id="th_role_manager_marketing" model="res.groups">
        <field name="name">Vai trò: Trưởng nhóm MKT</field>
        <field name="implied_ids" eval="[(4, ref('th_role_employee_marketing'))]"/>
    </record>

    <record id="th_role_employee_it_dev" model="res.groups">
        <field name="implied_ids" eval="[(4, ref('th_template_base_user_aum'))]"/>
        <field name="name">Vai trò: Nhân viên IT (Dev)</field>
    </record>

    <record id="th_role_employee_it_qa" model="res.groups">
        <field name="name">Vai trò: Nhân viên IT (QA)</field>
    </record>

    <record id="th_role_leader_it_qa" model="res.groups">
        <field name="name">Vai trò: Trưởng phòng IT(QA)</field>
    </record>

    <record id="th_role_leader_it_dev" model="res.groups">
        <field name="implied_ids" eval="[(4, ref('th_role_employee_it_dev'))]"/>
        <field name="name">Vai trò: Trưởng phòng IT(DEV)</field>
    </record>

    <record id="th_role_employee_it_support" model="res.groups">
        <field name="name">Vai trò: Nhân viên ITS</field>
    </record>

    <record id="th_role_leader_it_support" model="res.groups">
        <field name="name">Vai trò: Trưởng phòng ITS</field>
    </record>

    <record id="th_role_employee_document" model="res.groups">
        <field name="name">Vai trò: Nhân viên Hồ sơ</field>
        <field name="implied_ids" eval="[(4, ref('th_template_base_user_aum'))]"/>
    </record>

    <record id="th_role_leader_document" model="res.groups">
        <field name="name">Vai trò: Trưởng nhóm Hồ sơ</field>
        <field name="implied_ids" eval="[(4, ref('th_role_employee_document'))]"/>
    </record>

    <record id="th_role_partner_CRM" model="res.groups">
        <field name="name">Vai trò: Đối tác CRM</field>
    </record>

    <record id="th_role_partner_SRM" model="res.groups">
        <field name="name">Vai trò: Đối tác SRM</field>
    </record>

    <record id="th_role_partner_APM" model="res.groups">
        <field name="name">Vai trò: Đối tác APM</field>
    </record>

</odoo>