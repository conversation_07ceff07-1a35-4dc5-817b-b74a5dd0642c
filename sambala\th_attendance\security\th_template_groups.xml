<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--======================= Chấm công : <PERSON><PERSON> bộ =======================-->
        <record id="th_template_attendance_manager" model="res.groups">
            <field name="name">Mẫu: Chấm công: Cán bộ</field>
            <field name="implied_ids" eval="[(4, ref('th_access_th_attendance')), (4, ref('th_access_th_attendance_manager'))]"/>
        </record>

        <!-- template base user AUM implied template Chấm công / Người dùng và limited-->
        <record id="th_core_access_control.th_template_base_user_aum" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('th_access_th_attendance')), (4, ref('th_limited_th_attendance_base_user_aum'))]"/>
        </record>

        <!-- rule giới hạn quyền truy cập model -->
        <record id="th_rule_th_attendance_hr_attendance" model="ir.rule">
            <field name="name">th_rule_th_attendance_hr_attendance</field>
            <field name="model_id" ref="model_hr_attendance"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="0"/>
            <field name="groups" eval="[(4,ref('th_core_access_control.th_template_base_user_aum')),(4,ref('th_template_attendance_manager'))]"/>
        </record>

        <record id="th_rule_th_attendance_hr_attendance_overtime" model="ir.rule" >
            <field name="name">th_rule_th_attendance_hr_attendance_overtime</field>
            <field name="model_id" ref="hr_attendance.model_hr_attendance_overtime"/>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
            <field name="groups" eval="[(4,ref('th_core_access_control.th_template_base_user_aum'))]"/>
        </record>

    </data>
</odoo>