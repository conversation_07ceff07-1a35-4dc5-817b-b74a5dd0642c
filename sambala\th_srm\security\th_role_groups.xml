<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <!-- Role "Nhân viên TVTS" implied template "SRM: Nhân viên" và limited -->
        <record id="th_core_access_control.th_role_employee_tvts" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('th_template_srm_user')), (4, ref('th_limited_srm_user'))]"/>
        </record>

        <!-- Role "Nhân viên CSKH" implied template "SRM: Nhân viên" và limited -->
        <record id="th_core_access_control.th_role_employee_customer_care" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('th_template_srm_user')), (4, ref('th_limited_srm_user'))]"/>
        </record>

        <!-- Role "Nhân viên HCNS" implied template "SRM: Nhân viên" và limited -->
        <record id="th_core_access_control.th_role_employee_human_resources" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('th_template_srm_user')), (4, ref('th_limited_srm_user'))]"/>
        </record>

        <!-- Role "Nhân viên Kế toán" implied template "SRM: Nhân viên" và limited -->
        <record id="th_core_access_control.th_role_employee_accounting" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('th_template_srm_user')), (4, ref('th_limited_srm_user'))]"/>
        </record>

        <!-- Role "Nhân viên PTML" implied template "SRM: Nhân viên" và limited -->
        <record id="th_core_access_control.th_role_employee_network_development" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('th_template_srm_user')), (4, ref('th_limited_srm_user'))]"/>
        </record>

        <!-- Role "Nhân viên Vận hành" implied template "SRM: Nhân viên" và limited -->
        <record id="th_core_access_control.th_role_employee_operating" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('th_template_srm_user')), (4, ref('th_limited_srm_user'))]"/>
        </record>

        <!-- Role "Trưởng nhóm Vận hành" implied template "SRM: Trưởng nhóm" và limited -->
        <record id="th_core_access_control.th_role_leader_operating" model="res.groups">
            <field name="implied_ids" eval="[(4, ref('th_template_srm_manager')), (4, ref('th_limited_srm_manager'))]"/>
        </record>
    </data>
</odoo>
